# -*- coding: utf-8 -*-
import os
import sys
import glob
import random
import time
import datetime
from threading import Timer
import wave
from logger_config import setup_logger

# 设置日志
logger = setup_logger(module_name="hailuo_tts_service")

# 导入自定义模块
try:
    from new_code.config_loader import get_config
    from new_code.state_manager import can_play_idle_audio, set_playing_idle, set_processing
except ImportError:
    # 如果是直接运行这个文件，需要调整导入路径
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from new_code.config_loader import get_config
    from new_code.state_manager import can_play_idle_audio, set_playing_idle, set_processing

# 导入音频动作控制模块
# 暂时使用原始模块，后续可能会重构
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from audio_action_controller import play_idle_audio_with_action


# 全局变量
_config = None
_timer = None
_current_audio_index = 0  # 新增：记录当前播放的音频索引

def initialize():
    """初始化闲置音频管理器"""
    global _config, _current_audio_index
    _config = get_config()
    _current_audio_index = 0  # 重置音频索引
    
    # 确保音频目录存在
    os.makedirs(_config["idle_audio"]["directory"], exist_ok=True)
    logger.info(f"确保闲置音频目录存在: {_config['idle_audio']['directory']}")
    
    # 如果启用了基于时间的播放，确保上午和下午的音频目录存在
    if _config["idle_audio"].get("time_based_playback", False):
        morning_dir = os.path.join(_config["idle_audio"]["directory"], "morning")
        afternoon_dir = os.path.join(_config["idle_audio"]["directory"], "afternoon")
        friday_afternoon_dir = os.path.join(_config["idle_audio"]["directory"], "friday_afternoon")
        os.makedirs(morning_dir, exist_ok=True)
        os.makedirs(afternoon_dir, exist_ok=True)
        os.makedirs(friday_afternoon_dir, exist_ok=True)
        logger.info(f"确保上午音频目录存在: {morning_dir}")
        logger.info(f"确保下午音频目录存在: {afternoon_dir}")
        logger.info(f"确保星期五下午音频目录存在: {friday_afternoon_dir}")
    
    # 计划第一次播放
    schedule_next_play()
    logger.info("闲置音频管理器初始化完成，已安排首次播放")

def is_morning():
    """判断当前是否为上午时段"""
    current_hour = datetime.datetime.now().hour
    morning_start = _config["idle_audio"].get("morning_start", 6)  # 默认早上6点开始
    morning_end = _config["idle_audio"].get("morning_end", 12)  # 默认中午12点结束

    return morning_start <= current_hour < morning_end

def is_friday():
    """判断当前是否为星期五"""
    return datetime.datetime.now().weekday() == 4  # 星期五是4（0=星期一）

def play_random_audio():
    """播放随机闲置音频"""
    global _config, _current_audio_index
    
    if not _config:
        initialize()
    
    logger.info("尝试播放闲置音频...")
    
    # 判断当前是否能播放闲置音频
    if not can_play_idle_audio():
        logger.info("当前状态不允许播放闲置音频，跳过本次播放并重新调度")
        schedule_next_play()
        return
    
    # 获取所有可播放的音频文件
    audio_files = _get_audio_files()
    if not audio_files:
        logger.warning(f"未找到可播放的闲置音频文件，目录: {_config['idle_audio']['directory']}")
        schedule_next_play()
        return
    
    # 获取当前要播放的文件
    audio_file = audio_files[_current_audio_index]
    
    # 更新索引，实现循环
    _current_audio_index = (_current_audio_index + 1) % len(audio_files)

    logger.info(f"准备播放闲置音频: {audio_file}")
    
    try:
        # 设置状态为播放闲置音频
        logger.info("设置状态: is_playing_idle = True, is_processing = True")
        set_playing_idle(True)
        set_processing(True)
        
        # 播放音频
        logger.info(f"开始播放闲置音频: {audio_file}")
        before_play_time = time.time()
        
        # 调用音频动作控制器播放音频
        success = play_idle_audio_with_action(audio_file)
        
        play_duration = time.time() - before_play_time
        
        if not success:
            logger.error(f"播放闲置音频失败: {audio_file}, 耗时: {play_duration:.2f}秒")
        else:
            logger.info(f"闲置音频播放完成: {audio_file}, 耗时: {play_duration:.2f}秒")
            
    except Exception as e:
        logger.error(f"播放闲置音频时发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        
    finally:
        # 重置状态
        logger.info("重置状态: is_playing_idle = False, is_processing = False")
        set_playing_idle(False)
        set_processing(False)
        
        # 调度下一次播放
        logger.info("调度下一次闲置音频播放")
        schedule_next_play()

def schedule_next_play():
    """调度下一次闲置音频播放"""
    global _timer, _config
    
    if not _config:
        initialize()
    
    # 如果闲置音频播放功能已禁用，直接返回
    if not _config["idle_audio"]["enabled"]:
        logger.info("闲置音频播放功能已禁用，不再调度")
        return
    
    # 取消已有的计时器（如果有）
    if _timer is not None:
        _timer.cancel()
        logger.info("已取消现有的闲置音频播放计时器")
    
    # 随机生成下次播放的间隔时间
    min_interval = _config["idle_audio"]["min_interval"]
    max_interval = _config["idle_audio"]["max_interval"]
    # print(f"min_interval: {min_interval}, max_interval: {max_interval}")
    next_interval = random.randint(min_interval, max_interval)
    
    logger.info(f"计划在 {next_interval:.2f} 秒后播放下一个闲置音频")
    
    # 创建新的计时器
    _timer = Timer(next_interval, play_random_audio)
    _timer.daemon = True
    _timer.start()

def stop():
    """停止闲置音频管理器"""
    global _timer
    if _timer is not None:
        _timer.cancel()
        _timer = None
        logger.info("闲置音频管理器已停止")

def _get_audio_files():
    """根据当前时段获取可播放的音频文件"""
    global _config
    
    if not _config:
        initialize()
    
    # 获取基础音频目录
    base_audio_dir = _config["idle_audio"]["directory"]
    audio_files = []
    
    # 如果启用了基于时间的播放
    if _config["idle_audio"].get("time_based_playback", False):
        # 根据当前时段选择子目录
        if is_morning():
            time_period_dir = os.path.join(base_audio_dir, "morning")
            logger.info(f"当前为上午时段，使用上午音频目录: {time_period_dir}")
        else:
            # 检查是否为星期五下午
            if is_friday():
                friday_afternoon_dir = os.path.join(base_audio_dir, "friday_afternoon")
                # 先检查星期五下午目录是否有音频文件
                friday_audio_files = []
                for ext in ['*.wav', '*.WAV', '*.mp3', '*.MP3']:
                    friday_audio_files.extend(glob.glob(os.path.join(friday_afternoon_dir, ext)))

                if friday_audio_files:
                    time_period_dir = friday_afternoon_dir
                    logger.info(f"当前为星期五下午，使用星期五下午音频目录: {time_period_dir}")
                else:
                    time_period_dir = os.path.join(base_audio_dir, "afternoon")
                    logger.info(f"星期五下午目录无音频文件，使用普通下午音频目录: {time_period_dir}")
            else:
                time_period_dir = os.path.join(base_audio_dir, "afternoon")
                logger.info(f"当前为下午时段，使用下午音频目录: {time_period_dir}")

        # 查找所有wav和mp3文件（包括大小写变体，适配Linux系统）
        for ext in ['*.wav', '*.WAV', '*.mp3', '*.MP3']:
            audio_files.extend(glob.glob(os.path.join(time_period_dir, ext)))
        
        # 如果指定时段目录中没有音频文件，则使用基础目录
        if not audio_files:
            logger.warning(f"在时段目录 {time_period_dir} 中未找到音频文件，尝试使用基础目录")
            for ext in ['*.wav', '*.WAV', '*.mp3', '*.MP3']:
                audio_files.extend(glob.glob(os.path.join(base_audio_dir, ext)))
    else:
        # 如果未启用基于时间的播放，直接使用基础目录
        logger.info(f"未启用基于时间的播放，使用基础音频目录: {base_audio_dir}")
        for ext in ['*.wav', '*.WAV', '*.mp3', '*.MP3']:
            audio_files.extend(glob.glob(os.path.join(base_audio_dir, ext)))
    
    return audio_files

if __name__ == "__main__":
    # 测试闲置音频管理器
    initialize()
    
    print("闲置音频管理器已启动，按Ctrl+C退出...")
    try:
        # 保持主线程运行，等待定时器触发
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("正在停止闲置音频管理器...")
        stop()
        print("已退出") 