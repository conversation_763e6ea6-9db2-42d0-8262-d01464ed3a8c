import requests
import base64
import json
from pathlib import Path
# import logging
from datetime import datetime
from dataclasses import dataclass
from typing import Optional, Dict, Any
import uuid
import os
import sys
import time
import re
import wave
import numpy as np
from io import BytesIO
from threading import Thread, Event
from concurrent.futures import ThreadPoolExecutor
import yaml
from logger_config import setup_logger

# 设置日志
logger = setup_logger(module_name="hailuo_tts_service")

# 如果需要导入audio_action_controller
try:
    from audio_action_controller import play_audio_with_action
except ImportError:
    # 如果是直接运行这个文件，需要调整导入路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from audio_action_controller import play_audio_with_action

# 配置日志
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
# logger = logging.getLogger(__name__)

@dataclass
class ExtraInfo:
    audio_length: int  # 音频时长(毫秒)
    audio_sample_rate: int  # 采样率
    audio_size: int  # 音频大小(字节)
    bitrate: int  # 比特率
    audio_format: str  # 音频格式(mp3/pcm/flac)
    audio_channel: int  # 声道数(1:单声道, 2:双声道)
    invisible_character_ratio: float  # 非法字符占比
    usage_characters: int  # 计费字符数

@dataclass
class BaseResp:
    status_code: int  # 状态码
    status_msg: str  # 状态详情

@dataclass
class TTSResponse:
    audio: Optional[str] = None  # 音频数据(hex编码)
    subtitle_file: Optional[str] = None  # 字幕文件链接
    status: int = 0  # 合成状态(1:合成中, 2:合成结束)
    trace_id: Optional[str] = None  # 会话ID
    extra_info: Optional[ExtraInfo] = None  # 额外信息
    base_resp: Optional[BaseResp] = None  # 错误信息

class HailuoTTSService:
    def __init__(self):
        # 加载配置
        self.config = load_config()
        tts_config = self.config["api"]["hailuo_tts"]
        
        self.group_id = tts_config.get("url").split("GroupId=")[1] if "GroupId=" in tts_config.get("url", "") else "1919578801180778568"
        self.api_key = tts_config.get("api_key")
        self.url = tts_config.get("url")
        self.model = tts_config.get("model", "speech-02-turbo")
        self.voice_id = tts_config.get("voice", {}).get("id", "Robot_Armor")
        self.voice_weight = tts_config.get("voice", {}).get("weight", 100)
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 语音设置
        self.voice_setting = tts_config.get("voice_setting", {
            "speed": 1,
            "pitch": 0,
            "vol": 1,
            "latex_read": False
        })
        self.voice_setting["voice_id"] = self.voice_id
        # 音频设置
        self.audio_setting = tts_config.get("audio_setting", {
            "sample_rate": 32000,
            "bitrate": 128000,
            "format": "mp3"
        })
        
        # 创建输出目录
        output_dir = tts_config.get("output_dir", "yuyin/test_output")
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def text_to_speech(self, text, output_filename=None) -> Optional[TTSResponse]:
        """
        将文本转换为语音并保存为音频文件
        
        Args:
            text (str): 要转换的文本
            output_filename (str, optional): 输出文件名。如果未提供，将使用时间戳生成文件名
            
        Returns:
            Optional[TTSResponse]: 包含音频数据和相关信息的响应对象
        """
        try:
            payload = {
                "model": self.model,
                "text": text,
                "timber_weights": [
                    {
                        "voice_id": self.voice_id,
                        "weight": self.voice_weight
                    }
                ],
                "voice_setting": self.voice_setting,
                "audio_setting": self.audio_setting,
                "language_boost": "auto"
            }

            logger.info(f"正在将文本转换为语音: {text}")
            response = requests.post(self.url, headers=self.headers, json=payload)
            
            if response.status_code != 200:
                logger.error(f"API请求失败: {response.status_code} - {response.text}")
                return TTSResponse(
                    base_resp=BaseResp(
                        status_code=response.status_code,
                        status_msg=response.text
                    )
                )
            
            # 尝试解析JSON响应
            try:
                response_data = response.json()
                logger.info(f"API返回了JSON响应")
                logger.info(f"响应数据键: {list(response_data.keys())}")
                
                # 检查响应是否有错误
                if 'base_resp' in response_data:
                    base_resp = response_data.get('base_resp')
                    # 只有当status_code不为0时才认为是错误
                    if base_resp.get('status_code', 0) != 0:
                        logger.error(f"API返回错误: {base_resp}")
                        return TTSResponse(
                            base_resp=BaseResp(
                                status_code=base_resp.get('status_code', 1000),
                                status_msg=base_resp.get('status_msg', '未知错误')
                            )
                        )
                    else:
                        logger.info(f"API返回成功状态: {base_resp}")
                
                # 检查音频数据字段
                if 'data' in response_data:
                    # 获取hex编码的音频数据
                    audio_data = response_data.get('data')
                    logger.info(f"获取到音频数据类型: {type(audio_data)}")
                    
                    # 确保音频数据是字符串
                    if isinstance(audio_data, dict):
                        logger.info(f"音频数据是字典类型: {audio_data.keys()}")
                        # 尝试获取字典中的音频数据
                        if 'audio' in audio_data:
                            audio_hex = audio_data.get('audio')
                        else:
                            # 如果没有audio字段，尝试将整个字典转换为JSON字符串
                            audio_hex = json.dumps(audio_data)
                    elif isinstance(audio_data, str):
                        audio_hex = audio_data
                    else:
                        logger.error(f"未知的音频数据类型: {type(audio_data)}")
                        audio_hex = str(audio_data) if audio_data else ""
                    
                    logger.info(f"获取到音频数据，长度: {len(audio_hex) if audio_hex else 0}")
                    
                    # 获取其他响应字段
                    trace_id = response_data.get('trace_id', str(uuid.uuid4()))
                    status = response_data.get('status', 2)
                    
                    # 获取extra_info信息
                    extra_info_data = response_data.get('extra_info', {})
                    if extra_info_data:
                        logger.info(f"Extra Info字段: {list(extra_info_data.keys())}")
                        extra_info = ExtraInfo(
                            audio_length=extra_info_data.get('audio_length', 0),
                            audio_sample_rate=extra_info_data.get('audio_sample_rate', 32000),
                            audio_size=extra_info_data.get('audio_size', 0),
                            bitrate=extra_info_data.get('bitrate', 128000),
                            audio_format="mp3",
                            audio_channel=extra_info_data.get('audio_channel', 1),
                            invisible_character_ratio=extra_info_data.get('invisible_character_ratio', 0.0),
                            usage_characters=extra_info_data.get('usage_characters', len(text))
                        )
                    else:
                        # 如果没有extra_info，创建默认对象
                        extra_info = ExtraInfo(
                            audio_length=0,
                            audio_sample_rate=32000,
                            audio_size=0,
                            bitrate=128000,
                            audio_format="mp3",
                            audio_channel=1,
                            invisible_character_ratio=0.0,
                            usage_characters=len(text)
                        )
                    
                    # 创建响应对象
                    tts_response = TTSResponse(
                        audio=audio_hex,
                        status=status,
                        trace_id=trace_id,
                        extra_info=extra_info
                    )
                    
                    # 如果需要保存文件且有音频数据
                    if output_filename and audio_hex:
                        # 确保文件名以.mp3结尾
                        if not output_filename:
                            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                            output_filename = f"tts_{timestamp}.mp3"
                        
                        if not output_filename.lower().endswith('.mp3'):
                            output_filename += '.mp3'
                        
                        # 构建完整的输出路径
                        output_path = self.output_dir / output_filename
                        
                        # 保存音频文件
                        try:
                            # 将hex编码转换为二进制数据
                            audio_binary = bytes.fromhex(audio_hex)
                            
                            # 保存文件
                            with open(output_path, 'wb') as f:
                                f.write(audio_binary)
                            
                            logger.info(f"音频文件已保存: {output_path}")
                            print(f"音频文件已保存: {output_path}")  # 额外打印以便确认
                        except Exception as e:
                            logger.error(f"保存音频文件失败: {str(e)}")
                            print(f"保存音频文件失败: {str(e)}")  # 额外打印错误信息
                    
                    return tts_response
                else:
                    logger.error("响应中没有data字段")
                    logger.error(f"完整响应: {response_data}")
                    return TTSResponse(
                        base_resp=BaseResp(
                            status_code=1002,
                            status_msg="响应中没有音频数据"
                        )
                    )
                
            except ValueError as e:
                logger.error(f"解析JSON响应失败: {str(e)}")
                logger.error(f"响应内容: {response.text[:200]}...")  # 只打印前200个字符
                return TTSResponse(
                    base_resp=BaseResp(
                        status_code=1003,
                        status_msg=f"解析响应失败: {str(e)}"
                    )
                )
            
        except Exception as e:
            logger.error(f"转换过程中出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return TTSResponse(
                base_resp=BaseResp(
                    status_code=1000,
                    status_msg=f"未知错误: {str(e)}"
                )
            )

    def save_hex_audio(self, audio_hex: str, output_filename: str):
        """
        将hex编码的音频数据保存为文件
        """
        try:
            # 将hex字符串转换回二进制数据
            audio_data = bytes.fromhex(audio_hex)
            
            if not output_filename.lower().endswith('.wav'):
                output_filename += '.wav'
            
            output_path = self.output_dir / output_filename
            with open(output_path, 'wb') as f:
                f.write(audio_data)
            
            return str(output_path)
        except Exception as e:
            logger.error(f"保存音频文件失败: {str(e)}")
            return None

    def save_audio(self, audio_data, output_filename: str) -> Optional[str]:
        """
        将音频数据保存为文件
        
        Args:
            audio_data: 音频数据(可能是字符串、字典或其他格式)
            output_filename (str): 输出文件名
            
        Returns:
            Optional[str]: 成功时返回保存的文件路径，失败时返回None
        """
        try:
            if not audio_data:
                logger.error("没有音频数据可供保存")
                return None
            
            # 处理音频数据，确保它是字符串格式
            if isinstance(audio_data, dict):
                logger.info(f"音频数据是字典类型: {audio_data.keys() if hasattr(audio_data, 'keys') else '无键'}")
                if 'audio' in audio_data:
                    audio_hex = audio_data.get('audio')
                else:
                    # 尝试直接使用data字段
                    audio_hex = audio_data.get('data', json.dumps(audio_data))
            else:
                audio_hex = str(audio_data)
            
            # 确保文件名以.mp3结尾
            if not output_filename.lower().endswith('.mp3'):
                output_filename += '.mp3'
            
            # 构建完整的输出路径
            output_path = self.output_dir / output_filename
            
            # 确保输出目录存在
            self.output_dir.mkdir(parents=True, exist_ok=True)
            
            # 尝试解码数据并写入文件
            try:
                # 尝试当作hex解码
                audio_binary = bytes.fromhex(audio_hex)
            except ValueError as e:
                logger.error(f"解码十六进制音频数据失败: {str(e)}")
                logger.info("尝试直接写入字符串数据")
                audio_binary = audio_hex.encode('utf-8')
            
            # 保存文件
            with open(output_path, 'wb') as f:
                f.write(audio_binary)
            
            logger.info(f"音频文件已保存: {output_path}")
            return str(output_path)
        except Exception as e:
            logger.error(f"保存音频文件失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None

# 添加与tts_service.py兼容的接口函数
def initialize():
    """初始化TTS服务"""
    config = load_config()
    logger.info("海螺TTS服务初始化完成")
    return config

def synthesize_speech(text, output_path=None):
    """
    合成语音(与tts_service.py兼容的接口)
    
    Args:
        text (str): 要合成的文本
        output_path (str, optional): 输出文件路径
        
    Returns:
        list: 生成的临时文件列表
    """
    logger.info(f"开始合成语音: {text[:50]}...")
    
    # 确保输出目录存在
    if output_path:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 将text拆分成句子
    sentences = [s.strip() for s in re.split(r'[。；：！？;:!?]', text) if s.strip()]
    if not sentences:
        logger.warning("没有找到有效的句子")
        return []
    
    # 初始化TTS服务
    tts_service = HailuoTTSService()
    
    # 用于存储临时文件和事件同步的数据结构
    temp_files = []
    audio_segments = [None] * len(sentences)
    ready_events = [Event() for _ in range(len(sentences))]
    stop_event = Event()
    
    # 定义合成单个句子的函数
    def synthesize_segment(index, sentence):
        try:
            timestamp = time.time()
            filename = f"tts_{int(timestamp * 1000)}_{index}.mp3"
            
            logger.info(f"合成第{index + 1}句: {sentence}")
            # 修正参数顺序：先是文本，再是文件名
            response = tts_service.text_to_speech(sentence, filename)
            
            if response and response.audio:
                file_path = tts_service.output_dir / filename
                if os.path.exists(file_path):
                    logger.info(f"已生成第{index + 1}句音频: {file_path}")
                    
                    # 创建一个简单的音频段对象，与原代码类似
                    audio_segments[index] = {
                        'path': str(file_path),
                        'duration': response.extra_info.audio_length / 1000.0 if response.extra_info else 2.0
                    }
                    temp_files.append(str(file_path))
                    
                    # 通知播放线程该段已经准备好
                    ready_events[index].set()
                    return True
                else:
                    logger.error(f"第{index + 1}句音频文件未找到: {file_path}")
            else:
                logger.error(f"第{index + 1}句合成失败")
            
            # 如果合成失败，也设置事件以避免播放线程被阻塞
            ready_events[index].set()
            return False
        except Exception as e:
            logger.error(f"合成第{index + 1}句失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            ready_events[index].set()
            return False
    
    # 定义播放线程
    def play_segments():
        logger.info("播放线程启动")
        for i in range(len(sentences)):
            if stop_event.is_set():
                logger.warning("接收到停止信号，中断播放")
                break
                
            logger.info(f"等待第 {i + 1} 句准备就绪...")
            ready_events[i].wait(timeout=30)  # 添加30秒超时
            
            if not ready_events[i].is_set():
                logger.error(f"等待第 {i + 1} 句超时")
                continue
                
            segment = audio_segments[i]
            if not segment:
                logger.warning(f"第 {i + 1} 句合成结果为空，跳过")
                continue
            
            try:
                file_path = segment['path']
                if not os.path.exists(file_path):
                    logger.error(f"音频文件不存在: {file_path}")
                    continue
                    
                file_size = os.path.getsize(file_path)
                if file_size == 0:
                    logger.error(f"音频文件大小为0: {file_path}")
                    continue
                    
                logger.info(f"开始播放第 {i + 1} 句: {file_path}, 大小: {file_size}字节")
                
                # 播放音频
                success = play_audio_with_action(file_path)
                
                if success:
                    logger.info(f"成功播放第 {i + 1} 句")
                else:
                    logger.error(f"播放音频失败: {file_path}")
                    # 播放失败时等待预计的音频时长
                    audio_duration = segment['duration'] if 'duration' in segment else 2.0
                    logger.info(f"等待预计的音频时长: {audio_duration:.2f}秒")
                    time.sleep(audio_duration)
                
                # 播放完成后删除临时音频文件
                try:
                    os.remove(file_path)
                    logger.info(f"已删除音频文件: {file_path}")
                except Exception as e:
                    logger.error(f"删除音频文件失败: {file_path}, 错误: {e}")
            
            except Exception as e:
                logger.error(f"处理第 {i + 1} 句音频失败: {e}")
                import traceback
                logger.error(traceback.format_exc())
                time.sleep(1.0)  # 出错时等待一秒
        
        logger.info("播放线程结束")
    
    # 定义合成剩余句子的函数
    def synthesize_remaining_sentences():
        logger.info(f"开始异步合成剩余 {len(sentences)-1} 句")
        # 使用线程池在后台合成剩余句子
        with ThreadPoolExecutor(max_workers=min(3, len(sentences))) as executor:
            # 提交所有剩余句子的合成任务
            futures = [executor.submit(synthesize_segment, i, sentences[i]) for i in range(1, len(sentences))]
            # 等待所有任务完成
            for future in futures:
                future.result()
        logger.info("剩余句子合成线程完成")
    
    # 先同步合成第一句
    first_success = synthesize_segment(0, sentences[0])
    
    if not first_success:
        logger.error("第一句合成失败，不能开始播放")
        return []
    
    # 启动播放线程
    player_thread = Thread(target=play_segments, daemon=True)
    player_thread.start()
    
    # 如果有多个句子，启动剩余句子的合成线程
    if len(sentences) > 1:
        synth_thread = Thread(target=synthesize_remaining_sentences, daemon=True)
        synth_thread.start()
    
    # 等待播放线程完成
    player_thread.join()
    
    # 返回临时文件列表（此时大多数文件已被删除）
    return temp_files

def play_audio_file(audio_path):
    """播放本地音频文件（与tts_service.py兼容的接口）"""
    if not os.path.exists(audio_path):
        logger.error(f"音频文件不存在: {audio_path}")
        return False
        
    try:
        success = play_audio_with_action(audio_path)
        return success
    except Exception as e:
        logger.error(f"播放音频文件失败: {e}")
        return False

# 添加加载配置的函数
def load_config():
    """
    加载配置文件
    """
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'config.yaml')
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        # 返回默认配置
        return {
            "api": {
                "tts": {
                    "hailuo": {
                        "url": "https://api.minimax.chat/v1/t2a_v2?GroupId=1919578801180778568",
                        "api_key": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
                        "model": "speech-02-turbo",
                        "voice": {
                            "id": "Robot_Armor",
                            "weight": 100
                        },
                        "voice_setting": {
                            "speed": 1,
                            "pitch": 0,
                            "vol": 1,
                            "latex_read": False
                        },
                        "audio_setting": {
                            "sample_rate": 32000,
                            "bitrate": 128000,
                            "format": "mp3"
                        },
                        "output_dir": "yuyin/test_output"
                    }
                }
            }
        }

# 初始化模块
initialize()

if __name__ == "__main__":
    # 测试TTS服务
    def test_tts():
        # 初始化
        initialize()
        
        # 测试合成短句
        print("测试合成短句...")
        synthesize_speech("你好，我是恐龙。今天天气真好，我们一起玩吧！")
        
        # 等待合成完成
        print("测试完成")
    
    # 运行测试
    test_tts()
