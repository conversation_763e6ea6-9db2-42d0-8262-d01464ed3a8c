# 恐龙对话系统运行流程

## 系统架构

恐龙对话系统是一个模块化设计的语音交互系统，由多个相互协作的组件构成。系统采用单例模式确保各个组件全局唯一，并通过状态管理器协调各组件的工作状态，使闲置音频播放和对话处理不会发生冲突。

## 系统核心组件

1. **ConfigLoader**: 配置加载器，负责从YAML文件加载系统配置
2. **StateManager**: 状态管理器，管理系统状态并协调各组件工作
3. **IdleAudioManager**: 闲置音频管理器，在系统空闲时随机播放音频
4. **ConversationSystem**: 对话系统，处理唤醒、语音识别和对话流程
5. **AIService**: AI服务，与豆包API交互获取AI回复
6. **TTSService**: TTS服务，负责文本到语音的转换

## 系统启动流程

```mermaid
graph TD
    A[程序启动] --> B[解析命令行参数]
    B --> C{是否为测试模式?}
    C -- 是 --> D[运行测试模式]
    C -- 否 --> E[创建KonglongSystem实例]
    E --> F[初始化各组件]
    F --> G[检查AI服务可用性]
    G --> H[初始化闲置音频管理器]
    H --> I[播放启动提示音]
    I --> J[进入主循环]
```

## 主循环工作流程

```mermaid
graph TD
    A[主循环开始] --> B[检查对话超时]
    B --> C[检查唤醒词]
    C --> D{检测到唤醒词?}
    D -- 是 --> E[处理对话]
    D -- 否 --> F[短暂等待0.1秒]
    E --> F
    F --> A
```

## 唤醒词检测流程

```mermaid
sequenceDiagram
    participant M as 主循环
    participant C as 对话系统
    participant S as 状态管理器
    participant W as 外部唤醒词服务
    
    M->>C: check_for_wakeword()
    C->>S: can_start_conversation()
    S-->>C: 返回状态(是否可以开始对话)
    
    alt 可以开始对话
        C->>W: 发送唤醒检测请求
        W-->>C: 返回检测结果
        
        alt 检测到唤醒词
            C->>S: set_conversation(True)
            C->>S: set_processing(True)
            C-->>M: 返回True
        else 未检测到唤醒词
            C-->>M: 返回False
        end
    else 不可以开始对话
        C-->>M: 返回False
    end
```

## 对话处理流程

```mermaid
sequenceDiagram
    participant M as 主循环
    participant C as 对话系统
    participant R as 录音服务
    participant A as AI服务
    participant T as TTS服务
    
    M->>C: process_conversation()
    
    C->>C: 播放提示音
    C->>R: 发送录音请求
    R-->>C: 返回语音识别结果
    
    alt 有效识别结果
        C->>A: text_response(transcription)
        A-->>C: 返回AI回复
        
        C->>C: 提取文本和动作
        C->>T: synthesize_speech(clean_text)
        T-->>C: 合成并播放语音
        
        C-->>M: 返回True
    else 无效识别结果
        C-->>M: 返回False
    end
    
    C->>C: _end_conversation()
```

## 闲置音频管理流程

```mermaid
graph TD
    A[初始化闲置音频管理器] --> B[调度第一次播放]
    B --> C{是否可以播放闲置音频?}
    C -- 否 --> B
    C -- 是 --> D[查找可播放音频文件]
    D --> E{找到音频文件?}
    E -- 否 --> B
    E -- 是 --> F[随机选择一个音频文件]
    F --> G[设置播放状态]
    G --> H[播放闲置音频]
    H --> I[重置状态]
    I --> J[调度下一次播放]
    J --> B
```

## 状态管理与互斥机制

```mermaid
stateDiagram-v2
    [*] --> 空闲
    空闲 --> 对话中: 检测到唤醒词
    空闲 --> 播放闲置音频: 定时触发
    播放闲置音频 --> 空闲: 播放完成
    对话中 --> 空闲: 对话结束/超时
    
    state 空闲 {
        [*] --> 等待唤醒
        等待唤醒 --> 准备闲置音频: 定时器到期
        准备闲置音频 --> 等待唤醒: 无可用音频
    }
    
    state 对话中 {
        [*] --> 录音
        录音 --> AI处理: 录音完成
        AI处理 --> 语音合成: 获取AI回复
        语音合成 --> [*]: 播放完成
    }
```

## 测试模式流程

```mermaid
graph TD
    A[测试模式启动] --> B[获取AI和TTS服务实例]
    B --> C[检查AI服务可用性]
    C --> D{AI服务可用?}
    D -- 否 --> E[测试结束]
    D -- 是 --> F[获取测试文本]
    F --> G[调用AI服务]
    G --> H{获取到回复?}
    H -- 否 --> E
    H -- 是 --> I[解析文本和动作]
    I --> J[测试TTS服务]
    J --> E
```

## 优雅关闭流程

```mermaid
graph TD
    A[接收关闭信号] --> B[停止闲置音频管理器]
    B --> C[停止录音]
    C --> D[取消所有异步任务]
    D --> E[关闭事件循环]
    E --> F[程序退出]
``` 