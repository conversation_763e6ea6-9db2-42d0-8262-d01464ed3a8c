# -*- coding: utf-8 -*-
import yaml

from logger_config import setup_logger

# 设置日志
logger = setup_logger(module_name="ai_service")

# 全局配置变量
CONFIG = None

def load_config(config_path="./config/config.yaml"):
    """加载YAML配置文件"""
    global CONFIG
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            CONFIG = yaml.safe_load(file)
            logger.info(f"成功加载配置文件: {config_path}")
            return CONFIG
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        logger.warning("将使用默认配置")
        # 如果配置加载失败，使用默认值
        CONFIG = {
            "api": {
                "doubao": {
                    "api_key": "9336aaa1-e138-4376-8257-7e232f77e5ea",
                    "model": "doubao-1-5-lite-32k-250115",
                    "url": "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
                },
                "tts": {
                    "url": "http://192.168.20.181:9880/tts",
                    "ref_audio_path": "/home/<USER>/Desktop/nezha/qintianzhu.wav",
                    "speed_factor": 1.25
                }
            },
            "paths": {
                "output_path": "/home/<USER>/konglong/yuyin/response.wav"
            },
            "voice": {
                "sample_rate": 32000,
                "notification_sounds": [
                    {"name": "jxs.wav", "weight": 5},
                    {"name": "nxssm.wav", "weight": 3}
                ]
            },
            "conversation": {
                "timeout": 10,
                "max_history": 3
            },
            "services": {
                "wake_word": {"url": "http://127.0.0.1:8200"},
                "record": {"url": "http://127.0.0.1:8400"},
                "wake_service_type": "default"  # 只保留默认值
            },
            "rag_text": {
                "text": "你是一头恐龙，你会用中文回答问题，请提供简洁、有用的回答。"
            },
            "idle_audio": {
                "enabled": True,
                "directory": "yuyin",
                "min_interval": 30,
                "max_interval": 40,
                "time_based_playback": True,  # 是否启用基于时间的播放
                "time_periods": {
                    "morning": {
                        "start_hour": 6,  # 早晨开始时间（6点）
                        "start_minute": 0,
                        "end_hour": 9,    # 早晨结束时间（9点）
                        "end_minute": 0
                    },
                    "break_time": {
                        "start_hour": 9,  # 课间开始时间（9点）
                        "start_minute": 0,
                        "end_hour": 15,   # 课间结束时间（15点30分）
                        "end_minute": 30
                    },
                    "afternoon": {
                        "start_hour": 15, # 下午开始时间（15点30分）
                        "start_minute": 30,
                        "end_hour": 18,   # 下午结束时间（18点）
                        "end_minute": 0
                    }
                }
            }
        }
        return CONFIG

def get_config(config_path=None):
    """获取当前配置对象"""
    global CONFIG
    if CONFIG is None:
        return load_config(config_path) if config_path else load_config()
    return CONFIG

# 初始化时自动加载配置
if CONFIG is None:
    load_config()

if __name__ == "__main__":
    # 测试配置加载
    config = get_config()
    print(f"当前配置: {config}") 