# -*- coding: utf-8 -*-
import os
import sys
import time
import re
import wave
import glob
import numpy as np
import json
import requests
from io import BytesIO
from scipy.io.wavfile import read
from dataclasses import dataclass
from typing import Optional, List
from threading import Thread, Event
# from loguru import logger
from concurrent.futures import Thread<PERSON>oolExecutor
from logger_config import setup_logger

# 设置日志
logger = setup_logger()
# 导入自定义模块
try:
    from new_code.config_loader import get_config
except ImportError:
    # 如果是直接运行这个文件，需要调整导入路径
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from new_code.config_loader import get_config

# 导入音频动作控制模块
# 暂时使用原始模块，后续可能会重构
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from audio_action_controller import play_audio_with_action

# 配置loguru
# format_string = "[{time:HH:mm:ss.SSS}] {level: <8} | {file}:{line} - {message}"
#
# logger.remove()  # 删除默认处理器
# logger.add(sys.stderr, format=format_string)
# logger.add(
#     "logs/file_{time}.log",
#     rotation="50 MB",
#     retention="10 days",
#     format=format_string
# )

@dataclass
class AudioSegment:
    """音频片段数据类"""
    index: int
    path: str
    duration: float
    content: Optional[bytes] = None

# 全局变量
_config = None
_tts_url = None
_ref_audio_path = None
_speed_factor = None
_output_path = None

def initialize():
    """初始化TTS服务"""
    global _config, _tts_url, _ref_audio_path, _speed_factor, _output_path
    _config = get_config()
    _tts_url = _config["api"]["tts"]["url"]
    _ref_audio_path = _config["api"]["tts"]["ref_audio_path"]
    _speed_factor = _config["api"]["tts"]["speed_factor"]
    _output_path = _config["paths"]["output_path"]
    logger.info("TTS服务初始化完成")

def synthesize_speech(text, output_path=None):
    """合成语音(完整模式)"""
    global _output_path
    
    if not _output_path:
        initialize()
    
    if output_path is None:
        output_path = _output_path
        
    logger.info(f"开始合成语音: {text[:50]}...")
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    temp_files = _synthesize_and_play(text, output_path)
    return temp_files

def _synthesize_and_play(text, output_path):
    """合成并播放语音"""
    global _tts_url, _ref_audio_path, _speed_factor
    
    if not _tts_url or not _ref_audio_path:
        initialize()
    
    import re
    from threading import Event, Thread
    
    # 数据准备
    text = re.sub(r'\[.*?\]', '', text)  # 移除动作标记
    sentences = [s.strip() for s in re.split(r'[。；：，！？.;:,!?]', text) if s.strip()]
    
    if not sentences:
        logger.warning("没有找到有效的句子")
        return []
        
    audio_segments = [None] * len(sentences)
    ready_events = [Event() for _ in range(len(sentences))]
    stop_event = Event()
    temp_files = []
    
    total_start_time = time.time()
    
    # 定义播放线程
    def play_segments():
        logger.info("播放线程启动")
        for i in range(len(sentences)):
            if stop_event.is_set():
                logger.warning("接收到停止信号，中断播放")
                break
                
            logger.info(f"等待第 {i + 1} 句准备就绪...")
            ready_events[i].wait(timeout=30)  # 添加30秒超时
            
            if not ready_events[i].is_set():
                logger.error(f"等待第 {i + 1} 句超时")
                continue
                
            segment = audio_segments[i]
            if not segment:
                logger.warning(f"第 {i + 1} 句合成结果为空，跳过")
                continue
            
            try:
                if not os.path.exists(segment.path):
                    logger.error(f"音频文件不存在: {segment.path}")
                    continue
                    
                file_size = os.path.getsize(segment.path)
                if file_size == 0:
                    logger.error(f"音频文件大小为0: {segment.path}")
                    continue
                    
                logger.info(f"开始播放第 {i + 1} 句: {segment.path}, 大小: {file_size}字节")
                
                # 直接使用play_audio_with_action播放音频
                before_play = time.time()
                success = play_audio_with_action(segment.path)
                play_time = time.time() - before_play
                
                if success:
                    logger.info(f"成功播放第 {i + 1} 句, 用时: {play_time:.2f}秒")
                else:
                    logger.error(f"播放音频失败: {segment.path}")
                    # 播放失败时等待预计的音频时长
                    audio_duration = segment.duration if hasattr(segment, 'duration') else 2.0
                    logger.info(f"等待预计的音频时长: {audio_duration:.2f}秒")
                    time.sleep(audio_duration)
                
                # 删除临时音频文件
                if os.path.exists(segment.path):
                    try:
                        os.remove(segment.path)
                        logger.info(f"删除已播放的音频文件: {segment.path}")
                    except Exception as e:
                        logger.error(f"删除音频文件失败: {e}")
            
            except Exception as e:
                logger.error(f"处理第 {i + 1} 句音频失败: {e}")
                import traceback
                logger.error(traceback.format_exc())
                # 出错时等待一定时间
                time.sleep(segment.duration if hasattr(segment, 'duration') else 2.0)
        
        logger.info("播放线程结束")
    
    # 创建一个独立的合成线程函数来处理后续句子的合成
    def synthesize_remaining_sentences():
        logger.info(f"开始异步合成剩余 {len(sentences)-1} 句")
        # 使用线程池在后台合成剩余句子
        with ThreadPoolExecutor(max_workers=min(3, len(sentences) - 1)) as executor:
            # 提交所有剩余句子的合成任务，但不等待结果
            for i in range(1, len(sentences)):
                executor.submit(synthesize_segment, i, sentences[i])
        logger.info("剩余句子合成线程完成")
    
    # 定义合成单个句子的函数
    def synthesize_segment(index, sentence):
        segment_start_time = time.time()
        logger.info(f"开始合成第 {index + 1}/{len(sentences)} 句: {sentence}")
        
        try:
            # 使用与doubao_integration.py相同的参数
            payload = {
                "text": sentence,
                "text_lang": "zh",
                "ref_audio_path": _ref_audio_path,
                "aux_ref_audio_paths": [],
                "prompt_lang": "en",
                "prompt_text": "My name is Optimist Prime. We are autonomous robotic organisms from the planet cybertry Um.",
                "top_k": 32,
                "top_p": 1,
                "temperature": 1,
                "text_split_method": "cut5",
                "batch_size": 4,
                "batch_threshold": 0.75,
                "split_bucket": True,
                "speed_factor": _speed_factor,
                "fragment_interval": 0.25,
                "streaming_mode": False,
                "seed": 601722221,
                "sample_steps": 32,
                "media_type": "wav",
                "parallel_infer": True,
                "repetition_penalty": 2
            }
            
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'audio/wav'
            }
            
            tts_url = _tts_url
            logger.info(f"正在调用TTS URL: {tts_url}")
            response = requests.post(tts_url, json=payload, headers=headers, timeout=60)
            
            if response.status_code == 200:
                temp_output_path = os.path.join(os.path.dirname(output_path), f"response_{index}.wav")
                temp_files.append(temp_output_path)
                
                audio_buffer = BytesIO(response.content)
                sample_rate, audio_data = read(audio_buffer)
                
                if sample_rate != 32000:
                    from scipy import signal
                    logger.info(f"音频采样率 {sample_rate}Hz 不等于 32000Hz")
                    num_samples = round(len(audio_data) * 32000 / sample_rate)
                    audio_data = signal.resample(audio_data, num_samples)
                    sample_rate = 32000
                
                with wave.open(temp_output_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)
                    wav_file.setsampwidth(2)
                    wav_file.setframerate(32000)
                    wav_file.writeframes(audio_data.astype(np.int16).tobytes())
                
                duration = len(audio_data) / 32000.0
                
                segment = AudioSegment(index, temp_output_path, duration, None)
                audio_segments[index] = segment
                
                synth_time = time.time() - segment_start_time
                logger.info(f"合成第 {index + 1} 句完成"
                            f"时间: {duration:.2f}秒, "
                            f"采样率: 32000Hz, "
                            f"大小: {os.path.getsize(temp_output_path)}字节, "
                            f"耗时: {synth_time:.2f}秒")
                
                ready_events[index].set()
                return True
            else:
                logger.error(f"合成第 {index + 1} 句失败: {response.status_code}")
                logger.error(f"响应信息: {response.text}")
                ready_events[index].set()
                return False
        except Exception as e:
            logger.error(f"合成第 {index + 1} 句失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            ready_events[index].set()
            return False
    
    # 手动同步处理第一句，确保一开始就能够有声音输出
    first_segment_success = synthesize_segment(0, sentences[0])
    
    if not first_segment_success:
        logger.error("第一句合成失败，不能开始播放")
        return []
    
    # 启动播放线程
    player_thread = Thread(target=play_segments, daemon=True)
    player_thread.start()
    
    # 启动剩余句子的合成线程
    if len(sentences) > 1:
        synth_thread = Thread(target=synthesize_remaining_sentences, daemon=True)
        synth_thread.start()
    
    # 等待播放线程完成
    player_thread.join()
    
    total_duration = time.time() - total_start_time
    logger.info(f"合成播放完成，总耗时: {total_duration:.2f}秒")
    
    return temp_files

def play_audio_file(audio_path):
    """播放本地音频文件"""
    if not os.path.exists(audio_path):
        logger.error(f"音频文件不存在: {audio_path}")
        return False
        
    try:
        success = play_audio_with_action(audio_path)
        return success
    except Exception as e:
        logger.error(f"播放音频文件失败: {e}")
        return False

# 初始化模块
initialize()

if __name__ == "__main__":
    # 测试TTS服务
    def test_tts():
        # 初始化
        initialize()
        
        # 测试合成短句
        print("测试合成短句...")
        synthesize_speech("你好，我是恐龙。今天天气真好，我们一起玩吧！")
        
        # 等待合成完成
        time.sleep(5)
        print("测试完成")
    
    # 运行测试
    # test_tts() 
    #做一个并发测试
    import asyncio
    async def test_tts_concurrent():
        tasks = []
        for i in range(10):
            tasks.append(asyncio.create_task(synthesize_speech("你好，我是恐龙。今天天气真好，我们一起玩吧！")))
        results = await asyncio.gather(*tasks)
        print(results)
    asyncio.run(test_tts_concurrent())
