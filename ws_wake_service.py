import sys
import uvicorn
from fastapi import FastAPI, HTTPException
import pyaudio
import numpy as np
import aiohttp
import asyncio
from typing import List
from concurrent.futures import ThreadPoolExecutor
import time
from datetime import datetime
import yaml
from loguru import logger
import gc  # 添加垃圾回收模块
import gzip
import json
import uuid
import websockets
from io import BytesIO

app = FastAPI()
executor = ThreadPoolExecutor(max_workers=1)  # 减少工作线程数量

format_string = "[{time:HH:mm:ss.SSS}] {level: <8} | {file}:{line} - {message}"

logger.remove()  # 删除默认处理器
logger.add(sys.stderr, format=format_string)
logger.add(
    "logs/file_{time}.log", 
    rotation="10 MB",  # 减小日志文件大小
    retention="5 days",  # 减少保留天数
    format=format_string,
    enqueue=True  # 使用队列来减少IO阻塞
)

# 加载配置文件
def load_config(config_path="./config/config.yaml"):
    """加载YAML配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
            logger.info(f"成功加载配置文件: {config_path}")
            return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        logger.warning("将使用默认配置")
        return None

# 全局配置
CONFIG = load_config()
# 如果配置加载失败，使用默认值
if CONFIG is None:
    CONFIG = {
        "api": {
            "stt": {
                "ws_url": "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel"  # 只保留WebSocket URL配置
            }
        }
    }
    
WS_URL = CONFIG["api"]["stt"].get("ws_url", "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel")
# 添加全局录音器实例
global_recorder = None

def get_recorder():
    global global_recorder
    if global_recorder is None:
        global_recorder = AudioRecorder()
    return global_recorder

def print_with_timestamp(message):
    print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] {message}")


class AudioRecorder:
    def __init__(self):
        self.chunk = 4000
        self.format = pyaudio.paInt16
        self.channels = 1
        self.rate = 16000
        self.threshold = 60
        self.buffer_size = 16000
        self.silence_timeout = 1.2
        self.min_audio_length = 1.0
        self.p = None
        self.stream = None
        self.frames = []
        self.is_recording = False
        self.audio_buffer = np.array([], dtype=np.float32)
        self.last_sound_time = time.time()
        self.recording_started = False
        self.total_audio_length = 0
        self._recording_lock = asyncio.Lock()
        self.should_stop = False
        self.last_reset_time = time.time()
        self.reset_interval = 300
        self.speaking_extension = 0.5
        self.max_silence_count = 3
        self.silence_count = 0
        self.energy_threshold = 0.05
        self.sample_rate = 0.25
        self.device_restart_count = 0
        self.max_device_restart = 5
        self.last_deep_reset_time = time.time()
        self.deep_reset_interval = 3600
        self.last_transcription_time = time.time()
        self.transcription_interval = 1.0
        self.device_index = None  # 添加设备索引属性

    def find_input_device(self):
        """查找可用的输入设备"""
        if self.p is None:
            self.p = pyaudio.PyAudio()
        
        # 遍历所有音频设备
        for i in range(self.p.get_device_count()):
            device_info = self.p.get_device_info_by_index(i)
            print_with_timestamp(f"设备 {i}: {device_info['name']}")
            
            # 优先选择USB音频设备
            if ('usb' in device_info['name'].lower() and 
                device_info['maxInputChannels'] > 0):
                print_with_timestamp(f"选择USB音频设备: {device_info['name']}")
                return i
        
        # 如果没有找到USB设备，使用默认设备
        return self.p.get_default_input_device_info()['index']

    def start(self):
        """启动录音设备"""
        try:
            if self.p is None:
                self.p = pyaudio.PyAudio()
            
            # 如果没有指定设备索引，查找合适的设备
            if self.device_index is None:
                self.device_index = self.find_input_device()
            
            if self.stream is None or not self.stream.is_active():
                print_with_timestamp(f"正在使用设备索引 {self.device_index} 打开音频流")
                self.stream = self.p.open(
                    format=self.format,
                    channels=self.channels,
                    rate=self.rate,
                    input=True,
                    input_device_index=self.device_index,  # 指定输入设备
                    frames_per_buffer=self.chunk
                )
                print_with_timestamp("录音设备已启动")
                self.device_restart_count += 1
                print_with_timestamp(f"设备重启计数: {self.device_restart_count}/{self.max_device_restart}")
                
                if self.device_restart_count >= self.max_device_restart:
                    print_with_timestamp("设备重启次数过多，执行深度重置...")
                    self.deep_reset()
        except Exception as e:
            print_with_timestamp(f"启动录音设备失败: {e}")
            self.stop()
            self.deep_reset()
            raise

    def stop(self):
        """停止录音设备"""
        try:
            if self.stream:
                if self.stream.is_active():
                    self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            if self.p:
                self.p.terminate()
                self.p = None
            print_with_timestamp("录音设备已停止")
        except Exception as e:
            print_with_timestamp(f"停止录音设备失败: {e}")
            # 出现错误时，尝试深度重置
            self.deep_reset()

    def ensure_device_ready(self):
        """确保录音设备就绪"""
        try:
            # 检查是否需要定期深度重置
            current_time = time.time()
            if current_time - self.last_deep_reset_time > self.deep_reset_interval:
                print_with_timestamp("执行定期深度重置...")
                self.deep_reset()
                return
                
            if self.stream is None or not self.stream.is_active():
                self.start()
        except Exception as e:
            print_with_timestamp(f"确保设备就绪失败: {e}")
            time.sleep(0.5)  # 添加短暂延迟
            self.deep_reset()  # 出错时进行深度重置

    def check_reset_needed(self):
        """检查是否需要重置"""
        current_time = time.time()
        if current_time - self.last_reset_time > self.reset_interval:
            print_with_timestamp("已超过5分钟未唤醒，执行自动重置...")
            self.reset()
            self.last_reset_time = current_time
            return True
        return False

    def reset(self):
        """重置录音状态"""
        print_with_timestamp("重置录音状态...")
        self.frames = []
        self.is_recording = False
        self.recording_started = False
        self.should_stop = False
        self.audio_buffer = np.array([], dtype=np.float32)
        self.last_sound_time = time.time()
        self.total_audio_length = 0
        self.silence_count = 0
        # 强制垃圾回收
        gc.collect()
        print_with_timestamp("录音状态已重置")
        
    def deep_reset(self):
        """深度重置录音设备和所有状态"""
        print_with_timestamp("执行深度重置...")
        try:
            # 停止当前设备
            self.stop()
            
            # 重置所有计数器和标志
            self.reset()
            self.device_restart_count = 0
            self.last_deep_reset_time = time.time()
            
            # 强制释放资源
            self.audio_buffer = None
            self.frames = None
            
            # 等待系统释放资源
            time.sleep(0.5)
            
            # 强制垃圾回收
            gc.collect()
            
            # 重新初始化资源
            self.audio_buffer = np.array([], dtype=np.float32)
            self.frames = []
            
            # 重新启动设备
            time.sleep(0.5)  # 给系统一些时间
            self.start()
            
            print_with_timestamp("深度重置完成")
        except Exception as e:
            print_with_timestamp(f"深度重置过程中出错: {e}")
            # 最后的尝试：完全重新初始化对象
            time.sleep(1)
            try:
                self.stop()
                self.p = None
                self.stream = None
                gc.collect()
            except:
                pass

    def calculate_energy(self, audio_data):
        """计算音频能量，只使用部分采样点以降低CPU使用率"""
        # 仅使用部分采样点计算能量
        sampled_data = audio_data[::int(1/self.sample_rate)]
        if len(sampled_data) == 0:
            return 0
        return np.mean(np.abs(sampled_data))

    async def start_continuous_recording(self):
        """开始持续录音"""
        self.reset()
        self.is_recording = True
        self.silence_count = 0
        self.last_transcription_time = time.time()  # 初始化最后转录时间

        try:
            self.ensure_device_ready()
            error_counter = 0
            max_errors = 3
            
            while self.is_recording:
                try:
                    if self.device_restart_count >= self.max_device_restart:
                        print_with_timestamp("设备重启次数过多，执行深度重置...")
                        self.deep_reset()
                    
                    data = self.stream.read(self.chunk, exception_on_overflow=False)
                    audio_data = np.frombuffer(data, dtype=np.int16)
                    
                    error_counter = 0
                    
                    # 将音频数据添加到缓冲区
                    current_audio = audio_data.astype(np.float32) / 32768.0
                    self.audio_buffer = np.append(self.audio_buffer, current_audio)
                    
                    # 使用更高效的能量计算
                    current_energy = self.calculate_energy(audio_data)

                    if current_energy > self.energy_threshold:
                        if not self.recording_started:
                            print_with_timestamp("检测到声音，开始录音...")
                            self.recording_started = True

                        self.silence_count = 0
                        self.last_sound_time = time.time()
                        self.total_audio_length += len(current_audio) / self.rate

                    # 检查是否达到转录间隔且有足够的音频数据
                    current_time = time.time()
                    if current_time - self.last_transcription_time >= self.transcription_interval:
                        if len(self.audio_buffer) >= self.buffer_size:
                            print_with_timestamp(f"处理音频数据，长度: {len(self.audio_buffer)}")
                            yield self.audio_buffer[:self.buffer_size], False
                            self.audio_buffer = self.audio_buffer[self.buffer_size:]
                            self.last_transcription_time = current_time
                    
                    # 处理静音检测
                    elif self.recording_started:
                        silence_duration = time.time() - self.last_sound_time
                        if silence_duration > self.silence_timeout:
                            self.silence_count += 1
                            
                            if self.silence_count >= self.max_silence_count:
                                if self.total_audio_length >= self.min_audio_length:
                                    print_with_timestamp(f"检测到连续静音，录音时长: {self.total_audio_length:.2f}秒")
                                    if len(self.audio_buffer) > 0:
                                        yield self.audio_buffer, True
                                    else:
                                        yield np.array([]), True
                                    break
                                else:
                                    print_with_timestamp("录音时长太短，继续录音...")
                                    self.last_sound_time = time.time()
                                    self.silence_count = 0
                            else:
                                print_with_timestamp(f"检测到短暂静音 ({self.silence_count}/{self.max_silence_count})，延长检测时间...")
                                self.last_sound_time = time.time() + self.speaking_extension

                    # 控制缓冲区大小，防止内存溢出
                    if len(self.audio_buffer) > self.buffer_size * 3:
                        self.audio_buffer = self.audio_buffer[-self.buffer_size * 2:]
                    
                    await asyncio.sleep(0.01)  # 短暂休眠减少CPU使用率

                except Exception as e:
                    print_with_timestamp(f"读取音频错误: {e}")
                    error_counter += 1
                    
                    if error_counter >= max_errors:
                        print_with_timestamp(f"连续出现{max_errors}次错误，执行深度重置...")
                        self.deep_reset()
                        error_counter = 0
                    else:
                        self.ensure_device_ready()
                    
                    await asyncio.sleep(0.1)
                    continue

        finally:
            self.is_recording = False
            print_with_timestamp("录音循环已结束")
            gc.collect()

    def stop_recording(self):
        """停止录音"""
        print_with_timestamp(f"正在停止录音... (当前状态: is_recording={self.is_recording}, recording_started={self.recording_started})")
        self.is_recording = False
        self.recording_started = False
        self.should_stop = True  # 设置停止标志
        print_with_timestamp("录音标志已重置")
        # 清空缓冲区
        self.audio_buffer = np.array([], dtype=np.float32)
        # 等待一小段时间确保录音完全停止
        time.sleep(0.1)
        print_with_timestamp("录音已停止")
        # 强制垃圾回收
        gc.collect()


def load_wake_words() -> List[str]:
    try:
        with open('wake_words.txt', 'r', encoding='utf-8') as f:
            # 只加载少量唤醒词以提高效率
            words = [line.strip() for line in f.readlines() if line.strip()]
            # 如果唤醒词太多，只保留前5个
            if len(words) > 5:
                logger.warning(f"唤醒词过多，仅使用前5个: {words[:5]}")
                return words[:5]
            return words
    except FileNotFoundError:
        # 减少默认唤醒词数量
        return ["你好", "哪吒", "在吗"]


# 添加simplex_websocket_demo.py中的常量和函数
PROTOCOL_VERSION = 0b0001
DEFAULT_HEADER_SIZE = 0b0001

# Message Type:
FULL_CLIENT_REQUEST = 0b0001
AUDIO_ONLY_REQUEST = 0b0010
FULL_SERVER_RESPONSE = 0b1001
SERVER_ACK = 0b1011
SERVER_ERROR_RESPONSE = 0b1111

# Message Type Specific Flags
NO_SEQUENCE = 0b0000  # no check sequence
POS_SEQUENCE = 0b0001
NEG_SEQUENCE = 0b0010
NEG_WITH_SEQUENCE = 0b0011
NEG_SEQUENCE_1 = 0b0011

# Message Serialization
NO_SERIALIZATION = 0b0000
JSON = 0b0001

# Message Compression
NO_COMPRESSION = 0b0000
GZIP = 0b0001

def generate_header(
        message_type=FULL_CLIENT_REQUEST,
        message_type_specific_flags=NO_SEQUENCE,
        serial_method=JSON,
        compression_type=GZIP,
        reserved_data=0x00
):
    header = bytearray()
    header_size = 1
    header.append((PROTOCOL_VERSION << 4) | header_size)
    header.append((message_type << 4) | message_type_specific_flags)
    header.append((serial_method << 4) | compression_type)
    header.append(reserved_data)
    return header

def generate_before_payload(sequence: int):
    before_payload = bytearray()
    before_payload.extend(sequence.to_bytes(4, 'big', signed=True))  # sequence
    return before_payload

def parse_response(res):
    protocol_version = res[0] >> 4
    header_size = res[0] & 0x0f
    message_type = res[1] >> 4
    message_type_specific_flags = res[1] & 0x0f
    serialization_method = res[2] >> 4
    message_compression = res[2] & 0x0f
    reserved = res[3]
    header_extensions = res[4:header_size * 4]
    payload = res[header_size * 4:]
    result = {
        'is_last_package': False,
    }
    payload_msg = None
    payload_size = 0
    if message_type_specific_flags & 0x01:
        # receive frame with sequence
        seq = int.from_bytes(payload[:4], "big", signed=True)
        result['payload_sequence'] = seq
        payload = payload[4:]

    if message_type_specific_flags & 0x02:
        # receive last package
        result['is_last_package'] = True

    if message_type == FULL_SERVER_RESPONSE:
        payload_size = int.from_bytes(payload[:4], "big", signed=True)
        payload_msg = payload[4:]
    elif message_type == SERVER_ACK:
        seq = int.from_bytes(payload[:4], "big", signed=True)
        result['seq'] = seq
        if len(payload) >= 8:
            payload_size = int.from_bytes(payload[4:8], "big", signed=False)
            payload_msg = payload[8:]
    elif message_type == SERVER_ERROR_RESPONSE:
        code = int.from_bytes(payload[:4], "big", signed=False)
        result['code'] = code
        payload_size = int.from_bytes(payload[4:8], "big", signed=False)
        payload_msg = payload[8:]
    if payload_msg is None:
        return result
    if message_compression == GZIP:
        payload_msg = gzip.decompress(payload_msg)
    if serialization_method == JSON:
        payload_msg = json.loads(str(payload_msg, "utf-8"))
    elif serialization_method != NO_SERIALIZATION:
        payload_msg = str(payload_msg, "utf-8")
    result['payload_msg'] = payload_msg
    result['payload_size'] = payload_size
    return result

# 替换原来的call_stt_api_async函数
async def call_stt_api_async(audio_data):
    """使用WebSocket进行语音识别"""
    try:
        # 确保音频数据是numpy数组
        if not isinstance(audio_data, np.ndarray):
            print_with_timestamp(f"警告：音频数据类型不正确，当前类型: {type(audio_data)}")
            audio_data = np.array(audio_data)

        # 确保音频数据是一维数组
        if len(audio_data.shape) > 1:
            audio_data = audio_data.flatten()
            
        # 将浮点数据转换为int16
        audio_data_int16 = (audio_data * 32768).astype(np.int16).tobytes()
        
        print_with_timestamp(f"发送音频数据，长度={len(audio_data)}")
        
        # 准备WebSocket请求
        reqid = str(uuid.uuid4())
        seq = 1
        
        # 构建请求参数
        request_params = {
            "user": {
                "uid": "test",
            },
            "audio": {
                'format': "pcm",
                "sample_rate": 16000,
                "bits": 16,
                "channel": 1,
                "codec": "raw",
            },
            "request":{
                "model_name": "bigmodel",
                "enable_punc": True,
            }
        }
        
        # 准备请求头
        header = {}
        header["X-Api-Resource-Id"] = "volc.bigasr.sauc.duration"
        # 从配置文件config.yaml中读取，使用全局CONFIG而不是_config
        header["X-Api-Access-Key"] = CONFIG.get("api", {}).get("stt", {}).get("auth",{}).get("access_key", "NqMvkGLbf4dqG-TMgkOj-bGDGITEnPf9")
        header["X-Api-App-Key"] = CONFIG.get("api", {}).get("stt", {}).get("auth",{}).get("app_key", "3573626483")
        header["X-Api-Request-Id"] = reqid
        
        # 准备初始请求
        payload_bytes = str.encode(json.dumps(request_params))
        payload_bytes = gzip.compress(payload_bytes)
        full_client_request = bytearray(generate_header(message_type_specific_flags=POS_SEQUENCE))
        full_client_request.extend(generate_before_payload(sequence=seq))
        full_client_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
        full_client_request.extend(payload_bytes)
        
        # 准备音频数据请求
        seq += 1
        payload_bytes = gzip.compress(audio_data_int16)
        audio_only_request = bytearray(generate_header(message_type=AUDIO_ONLY_REQUEST, message_type_specific_flags=NEG_WITH_SEQUENCE))
        audio_only_request.extend(generate_before_payload(sequence=-seq))  # 使用负序号表示最后一个包
        audio_only_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
        audio_only_request.extend(payload_bytes)
        
        # 发送WebSocket请求
        async with websockets.connect(WS_URL, extra_headers=header, max_size=1000000000) as ws:
            # 发送初始请求
            await ws.send(full_client_request)
            res = await ws.recv()
            result = parse_response(res)
            
            # 发送音频数据
            await ws.send(audio_only_request)
            res = await ws.recv()
            result = parse_response(res)
            
            # 提取识别结果
            transcription = ""
            if 'payload_msg' in result and 'result' in result['payload_msg']:
                transcription = result['payload_msg']['result'].get('text', '')
                
            print_with_timestamp(f"STT 服务返回结果: {transcription}")
            return transcription
            
    except websockets.exceptions.ConnectionClosedError as e:
        print_with_timestamp(f"WebSocket连接关闭，状态码: {e.code}，原因: {e.reason}")
        return ""
    except websockets.exceptions.WebSocketException as e:
        print_with_timestamp(f"WebSocket连接失败: {e}")
        return ""
    except Exception as e:
        print_with_timestamp(f"调用STT服务错误: {e}")
        return ""

async def wake_word_detection():
    """唤醒词检测"""
    recorder = get_recorder()
    wake_words = load_wake_words()
    current_text = ""
    is_processing = False
    detection_attempt_count = 0  # 添加检测尝试计数
    max_detection_attempts = 10  # 最大连续检测尝试次数

    try:
        print_with_timestamp("准备检测唤醒词...")
        while True:
            # 检查是否需要重置
            recorder.check_reset_needed()
            
            # 检查连续尝试次数是否过多
            detection_attempt_count += 1
            if detection_attempt_count > max_detection_attempts:
                print_with_timestamp(f"连续检测尝试次数过多({detection_attempt_count})，执行深度重置...")
                recorder.deep_reset()
                detection_attempt_count = 0
                await asyncio.sleep(0.5)

            if is_processing:
                await asyncio.sleep(0.2)  # 增加休眠时间
                continue

            recorder.reset()
            is_processing = True

            try:
                async for audio_chunk, timeout in recorder.start_continuous_recording():
                    # 在循环中也检查是否需要重置
                    if recorder.check_reset_needed():
                        print_with_timestamp("执行定时重置，重新开始检测...")
                        break

                    # 检查是否应该停止
                    if recorder.should_stop:
                        print_with_timestamp("检测到停止信号，终止STT处理")
                        break

                    if timeout:
                        print_with_timestamp("检测到静音，重新开始检测唤醒词...")
                        break

                    if len(audio_chunk) == 0:
                        continue

                    # 如果设置了停止标志，不再调用STT服务
                    if recorder.should_stop:
                        break

                    # 降低发送到STT服务的频率
                    if len(audio_chunk) < 8000:  # 如果音频太短，跳过此次处理
                        continue

                    # 在调用STT之前检查一次状态
                    if not recorder.is_recording or recorder.should_stop:
                        break
                        
                    transcription = await call_stt_api_async(audio_chunk)
                    
                    # 再次检查停止标志和重置需求
                    if recorder.should_stop or recorder.check_reset_needed():
                        break

                    if transcription.strip():
                        current_text = transcription
                        print_with_timestamp(f"当前识别: '{current_text}'")
                        # 检测到有效文本，重置尝试计数
                        detection_attempt_count = 0

                        for wake_word in wake_words:
                            if wake_word in current_text:
                                print_with_timestamp(f"检测到唤醒词: {wake_word}")
                                # 更新最后重置时间，因为检测到了唤醒词
                                recorder.last_reset_time = time.time()
                                recorder.stop_recording()
                                return {
                                    "status": "success",
                                    "wake_word_detected": True,
                                    "word": wake_word,
                                    "transcription": current_text
                                }

                    # 每次处理后休眠一小段时间，减少CPU使用率
                    await asyncio.sleep(0.1)
            finally:
                is_processing = False
                recorder.stop_recording()
                # 强制垃圾回收
                gc.collect()

            # 如果设置了停止标志，退出主循环
            if recorder.should_stop:
                break
                
            # 每次检测循环后添加休眠
            await asyncio.sleep(0.2)

    except Exception as e:
        print_with_timestamp(f"唤醒词检测错误: {e}")
        recorder.stop_recording()
        # 出现异常，执行深度重置
        recorder.deep_reset()
        raise HTTPException(status_code=500, detail=str(e))

    return {
        "status": "success",
        "wake_word_detected": False,
        "word": "",
        "transcription": current_text
    }

@app.post("/wake")
async def listen_for_wake_word():
    """唤醒接口"""
    try:
        result = await wake_word_detection()
        if result["wake_word_detected"]:
            # 检测到唤醒词后，停止录音并等待新的请求
            recorder = get_recorder()
            recorder.stop_recording()
        return result
    except Exception as e:
        print(f"处理唤醒请求错误: {e}")
        # 出现异常时，尝试深度重置录音设备
        recorder = get_recorder()
        recorder.deep_reset()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/stop_recording")
async def stop_recording():
    """停止当前录音但不关闭服务"""
    try:
        if global_recorder:
            global_recorder.stop_recording()  # 停止录音
            await asyncio.sleep(0.2)  # 等待录音完全停止
            global_recorder.reset()  # 重置状态
            print_with_timestamp("录音已完全停止并重置")
        return {"status": "success", "message": "录音已停止"}
    except Exception as e:
        print_with_timestamp(f"停止录音失败: {e}")
        # 出现异常，执行深度重置
        if global_recorder:
            global_recorder.deep_reset()
        raise HTTPException(status_code=500, detail=str(e))

# 添加节能模式设置
@app.post("/set_power_saving")
async def set_power_saving(enabled: bool = True):
    """设置节能模式"""
    try:
        recorder = get_recorder()
        if enabled:
            # 在节能模式下调整参数
            recorder.chunk = 4800  # 更大的块大小
            recorder.sample_rate = 0.3  # 更低的采样率
            recorder.silence_timeout = 1.5  # 更长的静音超时
            print_with_timestamp("节能模式已启用")
        else:
            # 恢复正常模式
            recorder.chunk = 4000
            recorder.sample_rate = 0.25
            recorder.silence_timeout = 1.2
            print_with_timestamp("节能模式已禁用")
        return {"status": "success", "power_saving": enabled}
    except Exception as e:
        print_with_timestamp(f"设置节能模式失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 添加重置录音设备的接口
@app.post("/reset_device")
async def reset_device():
    """强制重置录音设备"""
    try:
        recorder = get_recorder()
        recorder.deep_reset()
        return {"status": "success", "message": "录音设备已重置"}
    except Exception as e:
        print_with_timestamp(f"重置录音设备失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "ws_wake_service:app",
        host="0.0.0.0",
        port=8200,
        log_level="info",
        workers=1,  # 减少worker数量
        loop="asyncio"  # 使用asyncio事件循环
    )