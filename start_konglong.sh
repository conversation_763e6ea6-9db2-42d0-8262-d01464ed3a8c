#!/bin/bash

# Define working directory
WORK_DIR="/home/<USER>/konglong3"
# Define virtual environment path
VENV_DIR="kl3"
# Define log directory
LOG_DIR="${WORK_DIR}/logs"

# Create log directory if not exists
mkdir -p ${LOG_DIR}

# Change to working directory
cd ${WORK_DIR} || exit 1

# Activate virtual environment
source ${VENV_DIR}/bin/activate || exit 1

# 设置环境变量
export PLATFORM="linux"

# 等待系统启动
sleep 10
# Define services
declare -A services=(
    # ["ws_wake_service"]="python ws_wake_service.py"
    # ["doubao_recorder"]="python doubao_audio_recorder_service.py"
    # ["main_service"]="python new_code/main.py"
    ["main_service"]="python new_code/idle_audio_manager.py"
)

# Start services
for name in "${!services[@]}"; do
    echo "Starting ${name}..."
    nohup ${services[$name]} > "${LOG_DIR}/${name}.log" 2>&1 &
    sleep 1
done

# Wait for services to start
sleep 3

# Check services status
echo "Checking services status:"
for name in "${!services[@]}"; do
    if pgrep -f "${services[$name]}" > /dev/null; then
        echo "${name} is running"
    else
        echo "${name} is not running"
    fi
done