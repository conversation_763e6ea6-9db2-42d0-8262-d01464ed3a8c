import paho.mqtt.client as mqtt
import time
import sys
from loguru import logger

# 配置日志记录
logger.remove()  # 移除默认的处理器
# 添加控制台输出
logger.add(sys.stdout, 
          format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
          level="INFO")
# 添加文件输出
logger.add("logs/mqtt_subscriber_{time}.log",  # 日志文件路径
           rotation="500 MB",                   # 日志文件大小超过500MB后自动轮换
           retention="10 days",                 # 日志保留10天
           compression="zip",                   # 压缩旧日志
           encoding="utf-8",                    # 使用utf-8编码
           level="DEBUG")                       # 文件记录DEBUG及以上级别的日志

# MQTT服务器配置
MQTT_BROKER = "broker.emqx.io"  # 这是EMQX的公共测试服务器，请替换为您的服务器地址
MQTT_PORT = 1883
TOPIC = "test/topic"  # 请替换为您要订阅的主题

# 连接成功回调函数
def on_connect(client, userdata, flags, rc):
    logger.info(f"已连接到MQTT服务器，返回码: {rc}")
    # 连接成功后订阅主题
    client.subscribe(TOPIC)
    logger.info(f"已订阅主题: {TOPIC}")

# 收到消息回调函数
def on_message(client, userdata, msg):
    logger.info(f"收到消息:")
    logger.info(f"主题: {msg.topic}")
    logger.info(f"消息内容: {msg.payload.decode()}")

def main():
    # 创建MQTT客户端实例
    client = mqtt.Client()
    
    # 设置回调函数
    client.on_connect = on_connect
    client.on_message = on_message
    
    try:
        # 连接到MQTT服务器
        logger.info(f"正在连接到MQTT服务器 {MQTT_BROKER}:{MQTT_PORT}")
        client.connect(MQTT_BROKER, MQTT_PORT, 60)
        
        # 开始循环，保持订阅状态
        client.loop_forever()
        
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
        client.disconnect()
    except Exception as e:
        logger.error(f"发生错误: {e}")
        client.disconnect()

if __name__ == "__main__":
    main() 