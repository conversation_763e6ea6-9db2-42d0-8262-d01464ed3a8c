# -*- coding: utf-8 -*-
import os
import sys
import time
import glob
import random
import asyncio
import aiohttp
import importlib
from logger_config import setup_logger

# 设置日志
logger = setup_logger(module_name="conversation_system")

# 导入自定义模块
try:
    from new_code.config_loader import get_config
    from new_code.state_manager import (
        can_start_conversation, set_conversation, set_processing,
        get_state_info
    )
    from new_code.ai_service import text_response, extract_text_and_actions
except ImportError:
    # 如果是直接运行这个文件，需要调整导入路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from new_code.config_loader import get_config
    from new_code.state_manager import (
        can_start_conversation, set_conversation, set_processing,
        get_state_info
    )
    from new_code.ai_service import text_response, extract_text_and_actions

# 导入音频动作控制模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from audio_action_controller import play_audio_with_action, play_idle_audio_with_action, mouth_action

# 配置loguru
# format_string = "[{time:HH:mm:ss.SSS}] {level: <8} | {file}:{line} - {message}"
#
# logger.remove()  # 删除默认处理器
# logger.add(sys.stderr, format=format_string)
# logger.add(
#     "logs/file_{time}.log",
#     rotation="5 MB",
#     retention="10 days",
#     format=format_string
# )

# 全局变量
_config = None
_last_response_time = 0
_conversation_timeout = 10  # 默认值，会被初始化函数更新
_notification_sounds = []
_available_sounds = []
_sound_weights = {}
_yuyin_folder = "yuyin"  # 语音文件夹路径
_tts_service = None


def initialize():
    """初始化对话系统"""
    global _config, _conversation_timeout, _notification_sounds
    global _available_sounds, _sound_weights

    _config = get_config()
    _conversation_timeout = _config["conversation"]["timeout"]

    # 初始化提示音
    _init_notification_sounds()

    logger.info("对话系统初始化完成")


def _init_notification_sounds():
    """初始化提示音"""
    global _config, _notification_sounds, _available_sounds, _sound_weights

    # 当前目录
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    # 从配置中获取提示音配置
    _notification_sounds = []  # 确保清空列表
    for sound in _config["voice"]["notification_sounds"]:
        if "path" in sound:
            sound_path = sound["path"]
            # 如果不是绝对路径，转换为绝对路径
            if not os.path.isabs(sound_path):
                sound_path = os.path.abspath(os.path.join(current_dir, sound_path))
        else:
            sound_path = os.path.abspath(os.path.join(current_dir, sound["name"]))

        _notification_sounds.append(sound_path)
        logger.info(f"添加提示音文件路径: {sound_path}")

    # 使用集合确保不重复添加
    _available_sounds = []
    added_paths = set()

    # 找出所有存在的提示音文件
    for sound_path in _notification_sounds:
        if sound_path in added_paths:
            logger.info(f"跳过重复的提示音文件: {sound_path}")
            continue

        # 检查文件是否存在
        if os.path.exists(sound_path):
            if os.path.isfile(sound_path):
                if os.access(sound_path, os.R_OK):
                    _available_sounds.append(sound_path)
                    added_paths.add(sound_path)
                    logger.info(f"找到可用提示音文件: {sound_path}")
                else:
                    logger.warning(f"提示音文件存在但没有读取权限: {sound_path}")
            else:
                logger.warning(f"提示音路径存在但不是文件: {sound_path}")
        else:
            # 尝试查找不同大小写的版本
            base_dir = os.path.dirname(sound_path)
            file_name = os.path.basename(sound_path)

            # 获取目录中所有文件并检查
            try:
                all_files = os.listdir(base_dir)
                for f in all_files:
                    if f.lower() == file_name.lower():
                        # 找到一个大小写不同的匹配
                        actual_path = os.path.join(base_dir, f)
                        logger.info(f"找到大小写不同的提示音文件: {file_name} -> {f}")

                        if os.path.isfile(actual_path) and os.access(actual_path, os.R_OK):
                            _available_sounds.append(actual_path)
                            added_paths.add(actual_path)
                            logger.info(f"添加大小写不同的提示音文件: {actual_path}")
                            break
                else:
                    logger.warning(f"提示音文件不存在: {sound_path}")
            except Exception as e:
                logger.warning(f"尝试查找大小写不同版本时出错: {e}")
                logger.warning(f"提示音文件不存在: {sound_path}")

    # 定义提示音权重映射表 - 使用配置中的权重
    _sound_weights = {}  # 确保清空字典
    for sound in _config["voice"]["notification_sounds"]:
        _sound_weights[sound["name"]] = sound["weight"]

    logger.info(f"初始化了 {len(_available_sounds)} 个提示音，共 {len(_notification_sounds)} 个配置")

    # 如果没有可用提示音，记录详细信息便于调试
    if not _available_sounds:
        logger.warning(f"没有可用的提示音，当前工作目录: {os.getcwd()}")
        logger.warning(f"代码所在目录: {current_dir}")
        logger.warning(f"系统类型: {sys.platform}")
        # 列出指定目录中的所有文件，帮助调试
        try:
            files = os.listdir(current_dir)
            logger.info(f"代码目录下的文件: {files}")
        except Exception as e:
            logger.error(f"无法列出目录内容: {e}")


def _select_notification_sound():
    """选择提示音"""
    global _available_sounds, _sound_weights

    if not _available_sounds:
        logger.warning("没有可用的提示音")
        return None

    # 准备权重列表，与available_sounds一一对应
    weights = []
    for sound_path in _available_sounds:
        # 提取文件名
        file_name = os.path.basename(sound_path)
        # 获取文件权重，如果不在权重表中则默认为1
        weight = _sound_weights.get(file_name, 1)
        weights.append(weight)

    # 使用加权随机选择
    audio_file = random.choices(_available_sounds, weights=weights, k=1)[0]
    file_name = os.path.basename(audio_file)
    weight = _sound_weights.get(file_name, 1)
    logger.info(f"本次加权随机选择提示音文件: {audio_file}, 权重: {weight}")

    return audio_file


def _get_random_yuyin_file():
    """随机获取yuyin文件夹中的一个mp3文件"""
    global _yuyin_folder

    try:
        # 确保使用绝对路径
        yuyin_dir = _yuyin_folder
        if not os.path.isabs(yuyin_dir):
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            yuyin_dir = os.path.join(base_dir, yuyin_dir)

        logger.warning(f"【随机播放】搜索目录: {yuyin_dir}")

        # 检查目录是否存在
        if not os.path.exists(yuyin_dir):
            logger.warning(f"【随机播放】目录不存在: {yuyin_dir}")
            return None

        # 列出目录下的所有文件，用于调试
        try:
            all_files = os.listdir(yuyin_dir)
            logger.warning(f"【随机播放】目录中的所有文件: {all_files}")
        except Exception as e:
            logger.error(f"【随机播放】列出目录内容失败: {e}")
            all_files = []

        # 获取yuyin文件夹中所有mp3文件
        mp3_pattern = os.path.join(yuyin_dir, "*.mp3")
        mp3_files = glob.glob(mp3_pattern)

        logger.warning(f"【随机播放】查找模式 '{mp3_pattern}' 找到 {len(mp3_files)} 个MP3文件")

        if not mp3_files:
            logger.warning(f"【随机播放】在 {yuyin_dir} 文件夹中未找到MP3文件")
            return None

        # 随机选择一个mp3文件
        random_mp3 = random.choice(mp3_files)
        logger.warning(f"【随机播放】随机选择yuyin文件: {random_mp3}")
        print(f"\n===== 【随机播放】随机选择yuyin文件: {random_mp3} =====\n")

        return random_mp3
    except Exception as e:
        logger.error(f"【随机播放】获取随机yuyin文件时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


async def _play_random_audio():
    """播放随机语音"""
    logger.warning("【随机播放】开始执行随机播放语音逻辑")
    print("\n===== 【随机播放】开始执行随机播放语音逻辑 =====\n")

    random_mp3 = _get_random_yuyin_file()

    if random_mp3:
        # 打印选择的文件名，便于调试
        file_name = os.path.basename(random_mp3)
        logger.warning(f"【随机播放】选择的语音文件: {file_name}")

        # 检查是否存在对应的动作文件
        action_file = random_mp3.rsplit('.', 1)[0] + '_action.yaml'
        if os.path.exists(action_file):
            logger.warning(f"【随机播放】找到对应的动作文件: {os.path.basename(action_file)}")
        else:
            logger.warning(f"【随机播放】未找到动作文件: {os.path.basename(action_file)}，将使用默认动作")

        # 播放随机选择的MP3文件（使用带动作控制的播放函数）
        logger.warning(f"【随机播放】播放音频和动作: {file_name}")

        success = play_idle_audio_with_action(random_mp3)
        if not success:
            logger.warning(f"【随机播放】语音 {file_name} 播放失败")
            return False
        else:
            logger.warning(f"【随机播放】语音 {file_name} 播放成功")
            return True
    else:
        logger.warning("【随机播放】未能获取随机语音文件")
        return False


async def check_for_wakeword():
    """检查是否检测到唤醒词"""
    global _config, _last_response_time

    if _config is None:
        initialize()

    # 检查当前是否可以开始对话
    if not can_start_conversation():
        # logger.info("当前状态不允许开始对话")
        return False

    # 直接使用默认的唤醒词检测
    result = await _check_for_wakeword_default()

    if result:
        # 设置状态
        set_conversation(True)
        set_processing(True)
        _last_response_time = time.time()

    return result


async def _check_for_wakeword_default():
    """使用默认服务检测唤醒词"""
    global _config

    try:
        async with aiohttp.ClientSession() as session:
            wake_word_url = _config["services"]["wake_word"]["url"]
            logger.info(f"发送唤醒检测请求到: {wake_word_url}/wake")

            async with session.post(f"{wake_word_url}/wake", timeout=60) as wake_response:
                logger.info(f"收到唤醒请求响应，状态码: {wake_response.status}")

                if wake_response.status in [200, 201]:
                    wake_data = await wake_response.json()
                    logger.info(f"收到唤醒请求数据: {wake_data}")

                    if wake_data.get("wake_word_detected"):
                        logger.info(f"检测到唤醒词: {wake_data.get('word')}")
                        return True
                    else:
                        logger.info("未检测到唤醒词")
                        return False
                else:
                    error_text = await wake_response.text()
                    logger.error(f"唤醒请求失败: 状态码 {wake_response.status}, 错误信息: {error_text}")
                    return False
    except aiohttp.ClientConnectorError as e:
        logger.error(f"无法连接到唤醒服务: {e}")
        return False
    except asyncio.TimeoutError:
        logger.error("唤醒请求超时")
        return False
    except Exception as e:
        logger.error(f"唤醒检测发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


async def record_and_transcribe():
    """录音并转写用户语音"""
    global _config

    if _config is None:
        initialize()

    try:
        # 检查当前状态，判断是首次唤醒还是连续对话
        state_info = get_state_info()
        is_continuous_dialog = state_info["in_conversation"] and not state_info["is_processing"]

        # 播放提示音 (仅在首次唤醒或配置要求时播放)
        if not is_continuous_dialog or _config.get("conversation", {}).get("always_play_notification", True):
            notification_sound = _select_notification_sound()
            if notification_sound:
                success = play_audio_with_action(notification_sound)
                if not success:
                    logger.warning("提示音播放失败，尝试直接进行录音")
                else:
                    logger.info("提示音播放成功，开始录音")
        else:
            logger.info("连续对话模式，跳过提示音播放，直接开始录音")

        # 设置处理状态
        set_processing(True)

        # 连续对话模式下，设置录音的最大重试次数
        max_retries = 2 if is_continuous_dialog else 1
        retry_count = 0

        while retry_count < max_retries:
            # 开始录音
            async with aiohttp.ClientSession() as session:
                record_url = _config["services"]["record"]["url"]
                logger.info(f"发送录音请求到: {record_url}/record")

                async with session.post(f"{record_url}/record") as record_response:
                    if record_response.status == 200:
                        record_data = await record_response.json()
                        transcription = record_data.get("transcription", "")

                        if not transcription:
                            if record_data.get("status") == "timeout":
                                # 如果在连续对话模式下且还有重试次数，则重新尝试
                                if is_continuous_dialog and retry_count < max_retries - 1:
                                    logger.info(f"连续对话模式下未检测到语音，尝试重新录音 ({retry_count + 1}/{max_retries})")
                                    retry_count += 1
                                    # 短暂等待后继续
                                    await asyncio.sleep(0.5)
                                    continue
                                else:
                                    # 如果已经尝试过或不在连续对话模式，结束对话状态
                                    logger.info("未检测到语音，结束对话")
                                    _end_conversation()
                                    return None

                            logger.info("检测到语音，但转录失败")
                            _end_conversation()
                            return None

                        logger.info(f"识别结果: {transcription}")
                        return transcription
                    else:
                        logger.error(f"录音请求失败: 状态码 {record_response.status}")
                        _end_conversation()
                        return None

    except aiohttp.ClientError as e:
        logger.error(f"录音请求客户端错误: {e}")
        _end_conversation()
        return None
    except Exception as e:
        logger.error(f"录音和转写过程发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        _end_conversation()
        return None


def get_tts_service():
    """获取TTS服务模块"""
    global _config, _tts_service
    
    if _tts_service is not None:
        return _tts_service
        
    tts_services = {
        "doubao": "new_code.doubao_tts_service",
        "hailuo": "new_code.hailuo_tts_service",
        "default": "new_code.tts_service"
    }
    
    tts_type = _config["api"]["tts"].get("type", "default")
    
    if tts_type not in tts_services:
        logger.warning(f"未知的TTS类型: {tts_type}，使用默认TTS服务")
        tts_type = "default"
    
    try:
        _tts_service = importlib.import_module(tts_services[tts_type])
        logger.info(f"已加载TTS服务: {tts_type}")
        return _tts_service
    except ImportError as e:
        logger.error(f"加载TTS服务失败: {e}")
        return None


def synthesize_speech(text):
    """统一的语音合成接口"""
    tts_service = get_tts_service()
    if tts_service:
        return tts_service.synthesize_speech(text)
    else:
        logger.error("无法加载TTS服务")
        return None


async def process_conversation():
    """处理整个对话流程"""
    global _last_response_time

    try:
        # 检查是否是连续对话模式
        state_info = get_state_info()
        is_continuous_dialog = state_info["in_conversation"] and not state_info["is_processing"]

        # 录音并转写
        transcription = await record_and_transcribe()

        # 如果没有获取到转写结果，直接结束对话
        if not transcription:
            logger.warning("未获取到有效转写结果，结束对话")
            _end_conversation()
            return False

        # 获取配置中的随机播放阈值
        random_threshold = _config.get("conversation", {}).get("random_response_threshold", 50) 

        # 生成一个0-100之间的随机整数
        rand_value = random.randint(0, 100)

        # 加强随机值的日志输出
        decision = "使用AI回复逻辑" if rand_value < random_threshold else "播放随机语音"
        logger.warning(f"【随机决策】生成随机数: {rand_value} —— 阈值: {random_threshold} —— {decision}")
        print(f"\n\n========= 【随机决策】生成随机数: {rand_value} —— 阈值: {random_threshold} —— {decision} ========\n\n")

        # 检查是否启用了连续对话功能
        continuous_dialog_enabled = _config.get("conversation", {}).get("continuous_dialog", True)

        if rand_value < random_threshold:
            # 调用AI获取回复
            start_time = time.time()
            ai_response, messages = await text_response(transcription)
            logger.info(f"AI处理时间: {time.time() - start_time:.2f}秒")

            if not ai_response:
                logger.warning("未获取到有效AI回复，结束对话")
                _end_conversation()
                return False

            # 提取文本和动作
            clean_text, actions = extract_text_and_actions(ai_response)
            logger.info(f"AI回复 (文本): {clean_text}")
            logger.info(f"提取动作: {actions}")

            # 合成并播放语音
            start_time = time.time()
            synthesize_speech(clean_text)
            logger.info(f"语音合成和播放时间: {time.time() - start_time:.2f}秒")

            if continuous_dialog_enabled:
                # 更新最后响应时间
                _last_response_time = time.time()
                # 设置为仍在对话中，但不在处理中，允许下一次用户输入
                set_conversation(True)
                set_processing(False)
                logger.info("AI对话已完成，等待用户继续输入")
            else:
                logger.info("未启用连续对话功能，结束对话")
                _end_conversation()
        else:
            # 播放随机语音
            success = await _play_random_audio()
            
            if success and continuous_dialog_enabled:
                # 只有在成功播放随机语音且启用连续对话时才继续
                _last_response_time = time.time()
                set_conversation(True)
                set_processing(False)
                logger.info("随机语音播放完成，等待用户继续输入")
            else:
                logger.info("随机语音播放结束，结束对话")
                _end_conversation()

        return True

    except Exception as e:
        logger.error(f"对话处理过程发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        _end_conversation()
        return False


def _end_conversation():
    """结束对话，重置状态"""
    logger.info("结束对话，重置状态")
    set_conversation(False)
    set_processing(False)


def check_timeout():
    """检查对话是否超时"""
    global _last_response_time, _conversation_timeout

    if _config is None:
        initialize()

    # 获取当前状态
    state_info = get_state_info()
    if state_info["in_conversation"]:
        current_time = time.time()
        if (current_time - _last_response_time) > _conversation_timeout:
            logger.info(f"对话超时 ({_conversation_timeout}秒)，自动结束")
            _end_conversation()
            return True
    return False


# 初始化模块
initialize()

if __name__ == "__main__":
    # 测试对话系统
    async def test():
        # 检查唤醒词
        print("等待唤醒词...")
        wakeword_detected = await check_for_wakeword()

        if wakeword_detected:
            print("检测到唤醒词，开始对话...")
            result = await process_conversation()
            print(f"对话处理结果: {result}")
        else:
            print("未检测到唤醒词")


    # 运行测试
    asyncio.run(test()) 