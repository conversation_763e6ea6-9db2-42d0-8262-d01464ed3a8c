# -*- coding: utf-8 -*-
import sys
import os
from datetime import datetime

from loguru import logger

def setup_logger(module_name=None, log_file_size="10 MB", retention="5 days"):
    """设置统一的日志配置
    
    Args:
        module_name: 模块名称，用于日志文件命名
        log_file_size: 日志文件大小限制
        retention: 日志保留时间
    """
    format_string = "[{time:HH:mm:ss.SSS}] {level: <8} | {file}:{line} - {message}"
    
    logger.remove()  # 删除默认处理器
    logger.add(sys.stderr, format=format_string)
    
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)
    
    # 使用年月日格式
    date_str = datetime.now().strftime("%Y%m%d")
    
    # 根据模块名称设置日志文件名
    file_name = f"logs/{module_name}_{date_str}.log" if module_name else f"logs/app_{date_str}.log"
    
    logger.add(
        file_name, 
        rotation=log_file_size, 
        retention=retention, 
        format=format_string,
        enqueue=True  # 使用队列来减少IO阻塞
    )
    
    return logger
#测试代码
if __name__ == '__main__':
    logger = setup_logger(module_name="123")
    logger.info('hello world')
