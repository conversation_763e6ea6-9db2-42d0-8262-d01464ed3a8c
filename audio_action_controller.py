import sys
import threading
import os
from pymodbus.client import ModbusTcpClient
import time
import random
import pygame
import yaml
from loguru import logger

format_string = "[{time:HH:mm:ss.SSS}] {level: <8} | {file}:{line} - {message}"

logger.remove()  # 删除默认处理器
logger.add(sys.stderr, format=format_string)
logger.add(
    "logs/file_{time}.log",
    rotation="5 MB",
    retention="7 days",
    format=format_string,
    enqueue=True
)


def optimize_audio_volume(sound, target_volume=0.92):
    """优化音频音量以减少杂音
    针对WAV无损格式优化，可以使用较高音量
    Args:
        sound: pygame.mixer.Sound对象
        target_volume: 目标音量 (0.0-1.0)，默认0.92适合WAV格式
    """
    try:
        # WAV是无损格式，可以使用较高音量，只需避免100%时的削波失真
        sound.set_volume(target_volume)
        logger.info(f"音频音量设置为: {target_volume * 100}% (针对WAV无损格式优化)")
        return True
    except Exception as e:
        logger.error(f"设置音频音量失败: {e}")
        return False


def get_wav_optimization_tips():
    """为WAV格式提供优化建议"""
    tips = [
        "WAV是无损格式，音质最佳",
        "如果仍有杂音，可能是：",
        "1. 音频文件本身录制时有问题",
        "2. 系统音频驱动问题",
        "3. 硬件音频输出问题",
        "4. 音频文件的位深度或采样率与系统不匹配"
    ]
    return tips


def check_audio_format(audio_path):
    """检查音频文件格式并给出优化建议"""
    try:
        file_extension = audio_path.lower().split('.')[-1]
        if file_extension == 'mp3':
            logger.info("检测到MP3文件，已应用针对压缩音频的优化设置")
            return 'mp3'
        elif file_extension in ['wav', 'flac']:
            logger.info("检测到WAV/FLAC无损音频文件，已应用高质量音频优化")
            return 'lossless'
        else:
            logger.info(f"检测到音频格式: {file_extension}")
            return 'other'
    except Exception as e:
        logger.warning(f"无法检测音频格式: {e}")
        return 'unknown'


def init_audio_system():
    """初始化音频系统，优化参数以减少杂音"""
    try:
        # 先退出之前的mixer（如果存在）
        pygame.mixer.quit()

        # 使用高质量音频参数初始化，针对WAV无损格式优化
        # frequency: 44100Hz 匹配您的音频采样率
        # size: -16 表示16位有符号音频
        # channels: 2 立体声匹配您的音频
        # buffer: 2048 适中的缓冲区，WAV格式不需要太大缓冲区
        pygame.mixer.pre_init(frequency=44100, size=-16, channels=2, buffer=2048)
        pygame.mixer.init()

        # 设置混音器的声道数量
        pygame.mixer.set_num_channels(8)

        logger.info("音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)")
        return True
    except Exception as e:
        logger.error(f"音频系统初始化失败: {e}")
        return False


def load_config(config_path="./config/config.yaml"):
    """加载YAML配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
            logger.info(f"成功加载配置文件: {config_path}")
            return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        logger.warning("将使用默认配置")
        return None


# 全局配置
CONFIG = load_config()
modbus_ip = CONFIG["modbus_server"]["ip"]
modbus_port = CONFIG["modbus_server"]["port"]

dataB = [];
# 连接到Modbus TCP服务器
client = ModbusTcpClient(modbus_ip, port=modbus_port)
client.connect()
# 120ms
a = '1031-2'  # 寄存地址以及模式
b = '1032-0'  # 嘴巴动作角度
b1 = '1032-100'  # 嘴巴动作角度
c = '1033-70'  # 嘴巴电机速度
dataB.append(a)
dataB.append(b1)
dataB.append(c)

neck_reg = '1034-0'
neck_reg1 = "1034-100"
neck_reg_speed = "1035-60"


def modbusSender(data):
    log_messages = []
    try:
        for i, item in enumerate(data):
            arr1, arr2 = item.split('-')
            log_messages.append(f"写入寄存器{arr1}，值为{arr2}")
            client.write_register(address=int(arr1), value=int(arr2), no_response_expected=False)
        # 批量记录日志
        if log_messages:
            logger.info("\n".join(log_messages))
    except Exception as e:
        print(f"Modbus发送失败: {e}")
        # 继续执行，不中断程序
        # pass


def modbusSender_bit(data):
    try:
        for i, item in enumerate(data):
            arr1, arr2 = item.split('-')
            logger.info(f"写入寄存器{arr1}，值为{arr2}")
            client.write_coil(address=int(arr1), value=int(arr2), no_response_expected=False)
    except Exception as e:
         logger.error(f"Modbus发送失败: {e}")
        # 继续执行，不中断程序
        # pass


def mouth_action(secend):
    try:
        sleep_time = int(secend / 0.15)
        logger.info(f"开始嘴部动作，计划执行{sleep_time}次")  # 只在开始时记录一次
        for i in range(sleep_time):
            dataA = []
            b2 = '1032-' + str(random.randint(10, 45))
            if i == sleep_time - 1:
                b2 = '1032-0'
            dataA.append(a)
            dataA.append(b2)
            dataA.append(c)
            modbusSender(dataA)
            time.sleep(0.15)
    except Exception as e:
        logger.error(f"嘴部动作控制失败: {e}")


def mouth_action_wait(secend):
    try:
        print(secend)
        sleep_time = int(secend / 0.12)  # 计算嘴巴动作次数
        for i in range(sleep_time):
            dataA = []
            b2 = '1032-' + str(random.randint(10, 45))
            if i == sleep_time - 1:
                b2 = '1032-0'
            dataA.append(a)
            dataA.append(b2)
            dataA.append('1033-100')
            modbusSender(dataA)
            time.sleep(0.12)
    except Exception as e:
        
        logger.error(f"嘴部动作控制失败: {e}")

        # 继续执行，不中断程序
        # pass


# 控制眼睛亮，1是亮，0是灭，写入寄存器地址是7999
def eyes_action(bit_type: int):
    result = bit_type
    a = f'7999-{result}'  # 眼睛寄存器地址
    try:
        modbusSender_bit([a])
    except Exception as e:
        logger.error(f"眼睛动作控制失败: {e}")
        # 继续执行，不中断程序
        # pass
        #写一个关于


# 脖子运动  脖子寄存器地址角度1034  速度1035
# neck_reg = '1034-0'
# neck_reg1 = "1034-100"
# neck_reg_speed = "1035-60"
def neck_action(secend):
    try:
        sleep_time = int(secend / 0.15)
        for i in range(sleep_time):
            dataB = []
            neck_reg = '1034-' + str(random.randint(10, 45))
            if i == sleep_time - 1:
                neck_reg = '1034-0'
            dataB.append(neck_reg)
            dataB.append(neck_reg1)
            dataB.append(neck_reg_speed)
            modbusSender(dataB)
            time.sleep(0.15)
    except Exception as e:
        logger.error(f"嘴部动作控制失败: {e}")
        # pass


def neck_action_wait(secend):
    try:
        print(secend)
        sleep_time = int(secend / 0.12)  # 计算嘴巴动作次数
        for i in range(sleep_time):
            dataB = []
            neck_reg = '1034-' + str(random.randint(10, 45))
            if i == sleep_time - 1:
                neck_reg = '1034-0'
            dataB.append(neck_reg)
            dataB.append(neck_reg1)
            dataB.append(neck_reg_speed)
            modbusSender(dataB)
            time.sleep(0.15)
    except Exception as e:
        logger.error(f"脖子动作控制失败: {e}")
        # pass


# 根据音频长度控制嘴部动作
def play_audio_with_action(audio_path, duration=None):
    try:
        # 初始化音频系统
        if not init_audio_system():
            logger.error("音频系统初始化失败，无法播放音频")
            return False
        # 检查音频格式
        audio_format = check_audio_format(audio_path)

        # 加载音频
        logger.info(f"正在加载音频: {audio_path}")
        sound = pygame.mixer.Sound(audio_path)

        # 根据音频格式优化音量
        if audio_format == 'lossless':
            optimize_audio_volume(sound, 0.92)  # WAV/FLAC无损格式可以使用较高音量
        elif audio_format == 'mp3':
            optimize_audio_volume(sound, 0.85)  # MP3使用较低音量
        else:
            optimize_audio_volume(sound, 0.9)   # 其他格式使用标准音量
        # 获取音频长度
        if duration is None:
            audio_length = sound.get_length()
            logger.info(f"音频长度(从文件获取): {audio_length}秒")
        else:
            audio_length = duration
            logger.info(f"音频长度(从参数获取): {audio_length}秒")

        # 创建一个线程来控制嘴部动作
        try:
            # 眼睛控制使用单独的线程，避免阻塞主线程
            eyes_thread = threading.Thread(target=eyes_action, args=(1,))
            eyes_thread.daemon = True  # 设置为后台线程
            eyes_thread.start()  # 启动线程

            # 嘴部动作线程
            mouth_thread = threading.Thread(target=mouth_action_wait, args=(audio_length,))
            mouth_thread.daemon = True  # 设置为后台线程，主线程结束时自动终止

            # 脖子动作线程
            neck_thread = threading.Thread(target=neck_action_wait, args=(audio_length,))
            neck_thread.daemon = True  # 设置为后台线程，主线程结束时自动终止

            # 播放音频并获取通道
            channel = sound.play()
            logger.info("开始播放音频")

            # 尝试启动动作控制线程，如果失败也不影响音频播放
            try:
                mouth_thread.start()
                logger.info("嘴部动作控制线程已启动")

                neck_thread.start()
                logger.info("脖子动作控制线程已启动")
            except Exception as e:
                logger.error(f"启动动作控制线程失败: {e}")
                # 不影响音频播放

            # 等待音频真正播放完毕
            while channel.get_busy():
                pygame.time.wait(100)  # 每100毫秒检查一次

            # 关闭眼睛 - 也用线程避免可能的延迟
            threading.Thread(target=eyes_action, args=(0,), daemon=True).start()
            # 停止播放
            pygame.mixer.stop()
            logger.info("音频播放完成")

            return True
        except Exception as e:
            logger.error(f"音频控制失败: {e}")
            # 如果出错，尝试优雅地结束
            pygame.mixer.quit()
            return False
    except Exception as e:
        logger.error(f"播放音频失败: {e}")
        return False


def run_predefined_actions(action_sequence):
    """执行预定义的动作序列"""
    try:
        for action in action_sequence:
            if isinstance(action, dict) and 'angle' in action and 'wait_time' in action:
                angle = action['angle']
                wait_time = action['wait_time']
                logger.info(f"执行动作: 角度={angle}, 等待时间={wait_time}秒")
                modbusSender([a, f'1032-{angle}', '1033-100'])
                time.sleep(wait_time)
            elif isinstance(action, (int, float)):
                # 兼容旧格式
                logger.info(f"执行动作: 角度={action}, 使用默认等待时间0.12秒")
                modbusSender([a, f'1032-{action}', '1033-100'])
                time.sleep(0.12)
            else:
                logger.warning(f"忽略无效的动作项: {action}")
    finally:
        logger.info("预定义动作序列结束，关闭嘴巴")
        modbusSender([a, '1032-0', '1033-100'])


def run_actions(action_sequence, audio_length):
    """执行动作控制
    Args:
        action_sequence: 预定义的动作序列，如果为None则使用随机动作
        audio_length: 音频长度，用于随机动作的时间控制
    """
    try:
        if action_sequence:
            run_predefined_actions(action_sequence)
        else:
            # 创建并启动嘴部动作线程
            mouth_thread = threading.Thread(target=mouth_action_wait, args=(audio_length,))
            mouth_thread.daemon = True

            # 创建并启动脖子动作线程
            neck_thread = threading.Thread(target=neck_action_wait, args=(audio_length,))
            neck_thread.daemon = True

            # 启动两个线程
            mouth_thread.start()
            neck_thread.start()

            # 等待两个线程完成
            mouth_thread.join()
            neck_thread.join()
    finally:
        logger.info("动作结束，关闭嘴巴和脖子")
        modbusSender([a, '1032-0', '1033-100'])  # 关闭嘴巴
        modbusSender([neck_reg, neck_reg1, neck_reg_speed])  # 重置脖子位置


def play_idle_audio_with_action(audio_path, duration=None):
    try:
        # 检查是否存在对应的动作文件
        action_file = audio_path.rsplit('.', 1)[0] + '_action.yaml'
        action_sequence = None

        # 如果存在动作文件，尝试加载
        if os.path.exists(action_file):
            try:
                with open(action_file, 'r', encoding='utf-8') as file:
                    action_sequence = yaml.safe_load(file)
                    logger.info(f"加载预定义动作: {action_file}")
            except Exception as e:
                logger.error(f"加载动作文件失败: {e}")

        # 初始化音频系统
        if not init_audio_system():
            logger.error("音频系统初始化失败，无法播放音频")
            return False
        # 检查音频格式
        audio_format = check_audio_format(audio_path)

        # 加载音频
        logger.info(f"正在加载音频: {audio_path}")
        sound = pygame.mixer.Sound(audio_path)

        # 根据音频格式优化音量
        if audio_format == 'lossless':
            optimize_audio_volume(sound, 0.92)  # WAV/FLAC无损格式可以使用较高音量
        elif audio_format == 'mp3':
            optimize_audio_volume(sound, 0.85)  # MP3使用较低音量
        else:
            optimize_audio_volume(sound, 0.9)   # 其他格式使用标准音量

        # 获取音频长度
        audio_length = duration if duration else sound.get_length()
        logger.info(f"音频长度: {audio_length}秒")

        # 创建线程控制嘴部和脖子动作
        action_thread = threading.Thread(
            target=run_actions,
            args=(action_sequence, audio_length),
            daemon=True
        )

        # 播放音频和动作
        eyes_thread = threading.Thread(target=eyes_action, args=(1,))
        eyes_thread.daemon = True  # 设置为后台线程
        eyes_thread.start()  # 启动线程
        # 播放音频并获取通道
        channel = sound.play()
        logger.info("开始播放音频")

        try:
            action_thread.start()
            logger.info("动作控制线程已启动")
        except Exception as e:
            logger.error(f"启动动作控制线程失败: {e}")

        # 等待音频真正播放完毕
        while channel.get_busy():
            pygame.time.wait(100)  # 每100毫秒检查一次

        # 停止播放
        pygame.mixer.stop()
        logger.info("音频播放完成")
        threading.Thread(target=eyes_action, args=(0,), daemon=True).start()

        return True
    except Exception as e:
        logger.error(f"播放音频失败: {e}")
        # 出错时也要确保嘴巴和脖子关闭
        try:
            modbusSender([a, '1032-0', '1033-100'])  # 关闭嘴巴
            modbusSender([neck_reg, neck_reg1, neck_reg_speed])  # 重置脖子位置
        except:
            pass
        pygame.mixer.quit()
        return False


if __name__ == "__main__":
    play_audio_with_action("D:/prooject_code/konglong3_2/yuyin/00001.mp3")
    # play_idle_audio_with_action("D:/prooject_code/konglong3_2/yuyin/00002.mp3")