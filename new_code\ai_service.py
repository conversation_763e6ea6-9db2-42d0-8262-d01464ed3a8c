# -*- coding: utf-8 -*-
import json
import sys
import os
import time
import asyncio
import aiohttp
import re
from logger_config import setup_logger

# 设置日志
logger = setup_logger(module_name="ai_service")

# 导入自定义模块
try:
    from new_code.config_loader import get_config
except ImportError:
    # 如果是直接运行这个文件，需要调整导入路径
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from new_code.config_loader import get_config

# 删除以下重复的日志配置代码
# format_string = "[{time:HH:mm:ss.SSS}] {level: <8} | {file}:{line} - {message}"
# logger.remove()  # 删除默认处理器
# logger.add(sys.stderr, format=format_string)
# logger.add(
#     "logs/file_{time}.log", 
#     rotation="50 MB", 
#     retention="10 days", 
#     format=format_string
# )

# 全局变量
_conversation_history = []
_config = None
_api_key = None
_model = None
_api_url = None

def initialize():
    """初始化AI服务"""
    global _config, _api_key, _model, _api_url
    _config = get_config()
    _api_key = _config["api"]["doubao"]["api_key"]
    _model = _config["api"]["doubao"]["model"]
    _api_url = _config["api"]["doubao"]["url"]
    logger.info("AI服务初始化完成")

async def check_availability():
    """检查豆包API是否可用"""
    global _api_key, _model, _api_url
    
    if not _api_key or not _model or not _api_url:
        initialize()
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {_api_key}"
    }
    
    payload = {
        "model": _model,
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant."
            },
            {
                "role": "user",
                "content": "测试连接"
            }
        ]
    }
  
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(_api_url, json=payload, headers=headers, timeout=5) as response:
                if response.status == 200:
                    logger.info("服务API连接正常")
                    return True
                else:
                    logger.error(f"服务API连接异常，状态码: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"服务API连接检查失败: {e}")
        return False

async def text_response(text, messages=None):
    """使用豆包API处理文本请求"""
    global _conversation_history, _config, _api_key, _model, _api_url
    
    if not _api_key or not _model or not _api_url:
        initialize()
    
    logger.info(f"收到用户输入: {text}")
    
    # 预处理文本
    parts = text.split("|>")
    text = parts[-1].strip()
    if not text:
        return "", messages if messages else _conversation_history

    # 使用提供的历史消息，或者使用内部存储的历史消息
    if messages is None:
        messages = _conversation_history
    
    # 截取历史消息，保持对话上下文不会太长
    max_history = _config["conversation"]["max_history"]
    if len(messages) >= max_history:
        messages = messages[-max_history:]
        logger.info(f"截取历史信息为前{max_history}条")

    # 构建豆包API请求
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {_api_key}"
    }

    # 从配置中获取RAG文本
    rag_text = _config["rag_text"]["text"]
    prompt_text = _config["system_prompt"]["text"]
    #输出分割线
    print("--------------------------------"*3)
    payload = {
        "model": _model,
        # "stream": true,
        "messages": [
            {
                "role": "system",
                "content": prompt_text + f"其他问题可以参考{rag_text}的内容进行回答,但是尽量不要回答已经回答过的重复内容"
            },
            {
                "role": "user",
                "content": text
            }
        ]
    }

    # 如果有历史消息，添加到请求中
    if messages and len(messages) > 0:
        # 将历史消息添加到payload中
        payload["messages"] = messages + payload["messages"]

    max_retries = 3
    retry_delays = [2, 4, 8]

    for retry in range(max_retries):
        logger.info(f"正在请求API (第 {retry + 1}/{max_retries})...")
        logger.info(f"请求参数: {json.dumps(payload, ensure_ascii=False)[:200]}...")

        try:
            async with aiohttp.ClientSession() as session:
                start_time = time.time()
                async with session.post(_api_url, json=payload, headers=headers) as response:
                    elapsed = time.time() - start_time
                    logger.info(f"API响应时间: {elapsed:.2f}秒, 状态码: {response.status}")

                    if response.status == 200:
                        response_data = await response.json()
                        logger.info(f"API响应结果: {json.dumps(response_data, ensure_ascii=False)[:200]}...")

                        if "choices" in response_data and len(response_data["choices"]) > 0:
                            content = response_data["choices"][0]["message"]["content"]
                            
                            # 更新对话历史
                            messages.append({"role": "user", "content": text})
                            messages.append({"role": "assistant", "content": content})
                            _conversation_history = messages
                            
                            logger.info(f"API响应成功: {content[:100]}...")

                            formatted_response = {
                                "response": content
                            }
                            return json.dumps(formatted_response), messages
                        else:
                            logger.error(f"API响应格式错误，预期有'choices'字段: {response_data}")
                    else:
                        logger.error(f"API请求失败，状态码: {response.status}")
                        try:
                            response_text = await response.text()
                            logger.info(f"响应文本: {response_text}")
                        except:
                            logger.info("无法获取API响应文本")

        except asyncio.TimeoutError:
            logger.error(f"API请求超时 (第 {retry + 1}/{max_retries})")
        except aiohttp.ClientConnectorError as e:
            logger.error(f"API请求失败，连接错误 (第 {retry + 1}/{max_retries}): {str(e)}")
        except aiohttp.ClientError as e:
            logger.error(f"API请求失败，客户端错误 (第 {retry + 1}/{max_retries}): {str(e)}")
        except Exception as e:
            logger.error(f"API请求失败，其他错误 (第 {retry + 1}/{max_retries}): {str(e)}")
            import traceback
            logger.error(f"异常信息: {traceback.format_exc()}")

        if retry < max_retries - 1:
            delay = retry_delays[retry]
            logger.info(f"等待 {delay} 秒...")
            await asyncio.sleep(delay)
        else:
            logger.info("API请求失败，默认返回")
            default_response = {
                "response": "抱歉，回答超时，请稍后再试。"
            }
            return json.dumps(default_response), messages

    return "", messages

def extract_text_and_actions(response_text):
    """从回复中提取文本和动作"""
    try:
        if not response_text or not isinstance(response_text, str):
            return "", []

        content = response_text.replace('```json', '').replace('```', '').strip()

        try:
            if content.strip().startswith("{"):
                response_dict = json.loads(content)
                content = response_dict.get("response", "")
            else:
                content = content.strip()
        except json.JSONDecodeError:
            content = content.strip()

        if not content:
            return "", []

        # 使用正则表达式提取动作标记
        actions = re.findall(r'\[(.*?)\]', content)
        clean_text = re.sub(r'\[.*?\]', '', content)
        clean_text = ' '.join(clean_text.split())

        return clean_text.strip(), actions
    except Exception as e:
        logger.error(f"提取文本失败: {str(e)}")
        return "", []

def clear_history():
    """清空对话历史"""
    global _conversation_history
    _conversation_history = []
    logger.info("对话历史已清空")

def get_conversation_history():
    """获取当前对话历史"""
    global _conversation_history
    return _conversation_history

# 初始化模块
initialize()

if __name__ == "__main__":
    # 测试AI服务
    async def test():
        # 检查API可用性
        available = await check_availability()
        print(f"API可用性: {available}")
        
        # 测试文本响应
        if available:
            response, history = await text_response("你好，恐龙")
            print(f"响应: {response}")
            
            # 提取文本和动作
            clean_text, actions = extract_text_and_actions(response)
            print(f"纯文本: {clean_text}")
            print(f"动作: {actions}")
    
    # 运行测试
    asyncio.run(test()) 