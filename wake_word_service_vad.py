import sys
import uvicorn
from fastapi import FastAPI, HTTPException
import pyaudio
import numpy as np
import aiohttp
import asyncio
import webrtcvad
from typing import List, Dict, Optional, Generator
from concurrent.futures import ThreadPoolExecutor
import time
from datetime import datetime
import yaml
import gc
from loguru import logger

app = FastAPI()
executor = ThreadPoolExecutor(max_workers=1)  # 减少工作线程数量

format_string = "[{time:HH:mm:ss.SSS}] {level: <8} | {file}:{line} - {message}"

logger.remove()  # 删除默认处理器
logger.add(sys.stderr, format=format_string)
logger.add(
    "logs/vad_{time}.log", 
    rotation="10 MB",  # 减小日志文件大小
    retention="5 days",  # 减少保留天数
    format=format_string,
    enqueue=True  # 使用队列来减少IO阻塞
)

# 加载配置文件
def load_config(config_path="./config/config.yaml"):
    """加载YAML配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
            logger.info(f"成功加载配置文件: {config_path}")
            return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        logger.warning("将使用默认配置")
        return None

# 全局配置
CONFIG = load_config()
# 如果配置加载失败，使用默认值
if CONFIG is None:
    CONFIG = {
        "api": {
            "stt": {
                "url": "http://192.168.20.181:8100/stt/"
            }
        }
    }
    
STT_URL = CONFIG["api"]["stt"]["url"]
# 添加全局录音器实例
global_recorder = None

def get_recorder():
    global global_recorder
    if global_recorder is None:
        global_recorder = VADRecorder()
    return global_recorder

def print_with_timestamp(message):
    print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] {message}")


class VADRecorder:
    def __init__(self):
        # 音频参数
        self.chunk = 3200
        self.format = pyaudio.paInt16
        self.channels = 1
        self.rate = 16000
        self.buffer_size = 16000
        self.min_audio_length = 0.5  # 最小音频长度（秒）
        
        # VAD参数
        self.vad = webrtcvad.Vad()
        self.vad.set_mode(3)  # 3是最敏感的模式
        self.vad_frame_duration_ms = 30  # VAD需要10, 20, 或30ms的帧
        self.vad_frame_size = int(self.rate * self.vad_frame_duration_ms / 1000)
        self.vad_buffer = []  # 暂存需要进行VAD检测的音频
        self.speech_frames_threshold = 3  # 连续检测到语音的帧数阈值
        self.non_speech_frames_threshold = 10  # 连续检测到非语音的帧数阈值

        # 状态变量
        self.p = None
        self.stream = None
        self.frames = []
        self.is_recording = False
        self.audio_buffer = np.array([], dtype=np.float32)
        self.last_sound_time = time.time()
        self.recording_started = False
        self.total_audio_length = 0  # 记录总音频长度
        self._recording_lock = asyncio.Lock()  # 添加锁来控制录音状态
        self.should_stop = False  # 添加停止标志
        self.last_reset_time = time.time()  # 添加最后重置时间
        self.reset_interval = 300  # 5分钟 = 300秒
        self.speech_frames_count = 0  # 连续检测到语音的帧数
        self.non_speech_frames_count = 0  # 连续检测到非语音的帧数
        
        # 设备维护
        self.device_restart_count = 0  # 记录设备重启次数
        self.max_device_restart = 5  # 最大设备重启次数，超过则完全重置
        self.last_deep_reset_time = time.time()  # 上次深度重置的时间
        self.deep_reset_interval = 3600  # 1小时 = 3600秒，定期深度重置

    def start(self):
        """启动录音设备"""
        try:
            if self.p is None:
                self.p = pyaudio.PyAudio()
            if self.stream is None or not self.stream.is_active():
                self.stream = self.p.open(
                    format=self.format,
                    channels=self.channels,
                    rate=self.rate,
                    input=True,
                    frames_per_buffer=self.chunk
                )
                print_with_timestamp("录音设备已启动")
                self.device_restart_count += 1
                print_with_timestamp(f"设备重启计数: {self.device_restart_count}/{self.max_device_restart}")
                
                # 如果重启次数过多，执行深度重置
                if self.device_restart_count >= self.max_device_restart:
                    print_with_timestamp("设备重启次数过多，执行深度重置...")
                    self.deep_reset()
        except Exception as e:
            print_with_timestamp(f"启动录音设备失败: {e}")
            self.stop()
            # 尝试完全重新初始化设备
            self.deep_reset()
            raise

    def stop(self):
        """停止录音设备"""
        try:
            if self.stream:
                if self.stream.is_active():
                    self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            if self.p:
                self.p.terminate()
                self.p = None
            print_with_timestamp("录音设备已停止")
        except Exception as e:
            print_with_timestamp(f"停止录音设备失败: {e}")
            # 出现错误时，尝试深度重置
            self.deep_reset()

    def ensure_device_ready(self):
        """确保录音设备就绪"""
        try:
            # 检查是否需要定期深度重置
            current_time = time.time()
            if current_time - self.last_deep_reset_time > self.deep_reset_interval:
                print_with_timestamp("执行定期深度重置...")
                self.deep_reset()
                return
                
            if self.stream is None or not self.stream.is_active():
                self.start()
        except Exception as e:
            print_with_timestamp(f"确保设备就绪失败: {e}")
            time.sleep(0.5)  # 添加短暂延迟
            self.deep_reset()  # 出错时进行深度重置

    def check_reset_needed(self):
        """检查是否需要重置"""
        current_time = time.time()
        if current_time - self.last_reset_time > self.reset_interval:
            print_with_timestamp("已超过5分钟未唤醒，执行自动重置...")
            self.reset()
            self.last_reset_time = current_time
            return True
        return False

    def reset(self):
        """重置录音状态"""
        print_with_timestamp("重置录音状态...")
        self.frames = []
        self.is_recording = False
        self.recording_started = False
        self.should_stop = False
        self.audio_buffer = np.array([], dtype=np.float32)
        self.vad_buffer = []
        self.speech_frames_count = 0
        self.non_speech_frames_count = 0
        self.last_sound_time = time.time()
        self.total_audio_length = 0
        # 强制垃圾回收
        gc.collect()
        print_with_timestamp("录音状态已重置")
        
    def deep_reset(self):
        """深度重置录音设备和所有状态"""
        print_with_timestamp("执行深度重置...")
        try:
            # 停止当前设备
            self.stop()
            
            # 重置所有计数器和标志
            self.reset()
            self.device_restart_count = 0
            self.last_deep_reset_time = time.time()
            
            # 强制释放资源
            self.audio_buffer = None
            self.frames = None
            self.vad_buffer = None
            
            # 等待系统释放资源
            time.sleep(0.5)
            
            # 强制垃圾回收
            gc.collect()
            
            # 重新初始化资源
            self.audio_buffer = np.array([], dtype=np.float32)
            self.frames = []
            self.vad_buffer = []
            
            # 重新启动设备
            time.sleep(0.5)  # 给系统一些时间
            self.start()
            
            print_with_timestamp("深度重置完成")
        except Exception as e:
            print_with_timestamp(f"深度重置过程中出错: {e}")
            # 最后的尝试：完全重新初始化对象
            time.sleep(1)
            try:
                self.stop()
                self.p = None
                self.stream = None
                gc.collect()
            except:
                pass

    def is_speech(self, audio_data):
        """使用VAD检测音频是否包含语音"""
        try:
            # 确保数据长度适合VAD
            if len(audio_data) < self.vad_frame_size:
                return False
                
            # 拆分成VAD可处理的帧大小
            frames = []
            for i in range(0, len(audio_data) - self.vad_frame_size + 1, self.vad_frame_size):
                frames.append(audio_data[i:i+self.vad_frame_size].tobytes())
                
            # 检查每一帧是否包含语音
            speech_detected = 0
            for frame in frames:
                if self.vad.is_speech(frame, self.rate):
                    speech_detected += 1
                    
            # 如果超过50%的帧被检测为语音，认为整段音频包含语音
            return speech_detected > len(frames) * 0.5
        except Exception as e:
            print_with_timestamp(f"VAD检测错误: {e}")
            return False

    async def start_continuous_recording(self):
        """开始持续录音"""
        self.reset()  # 重置状态
        self.is_recording = True

        try:
            self.ensure_device_ready()
            
            # 错误计数
            error_counter = 0
            max_errors = 3
            
            while self.is_recording:
                try:
                    # 检查是否出现过多重启
                    if self.device_restart_count >= self.max_device_restart:
                        print_with_timestamp("设备重启次数过多，执行深度重置...")
                        self.deep_reset()
                    
                    # 读取音频
                    data = self.stream.read(self.chunk, exception_on_overflow=False)
                    audio_data = np.frombuffer(data, dtype=np.int16)
                    
                    # 重置错误计数
                    error_counter = 0
                    
                    # 添加到VAD缓冲区
                    self.vad_buffer.append(audio_data)
                    
                    # 保持VAD缓冲区不超过3秒
                    max_buffer_size = int(3 * self.rate / self.chunk)
                    if len(self.vad_buffer) > max_buffer_size:
                        self.vad_buffer.pop(0)
                    
                    # 将当前音频转换为浮点数并添加到音频缓冲区
                    current_audio = audio_data.astype(np.float32) / 32768.0
                    self.audio_buffer = np.append(self.audio_buffer, current_audio)
                    
                    # 合并VAD缓冲区用于检测
                    vad_data = np.concatenate(self.vad_buffer) if self.vad_buffer else np.array([])
                    
                    # 使用VAD检测是否包含语音
                    contains_speech = self.is_speech(vad_data) if len(vad_data) > 0 else False
                    
                    if contains_speech:
                        # 检测到语音
                        self.speech_frames_count += 1
                        self.non_speech_frames_count = 0
                        
                        if self.speech_frames_count >= self.speech_frames_threshold and not self.recording_started:
                            print_with_timestamp("VAD检测到语音，开始录音...")
                            self.recording_started = True
                            self.last_sound_time = time.time()
                            
                        if self.recording_started:
                            self.total_audio_length += len(current_audio) / self.rate
                            
                            if len(self.audio_buffer) >= self.buffer_size:
                                yield self.audio_buffer[:self.buffer_size], False
                                self.audio_buffer = self.audio_buffer[self.buffer_size:]
                    else:
                        # 未检测到语音
                        self.speech_frames_count = 0
                        
                        if self.recording_started:
                            self.non_speech_frames_count += 1
                            
                            # 计算静音时间
                            silence_duration = time.time() - self.last_sound_time
                            
                            # 连续检测到足够多的非语音帧，认为语音结束
                            if self.non_speech_frames_count >= self.non_speech_frames_threshold:
                                if self.total_audio_length >= self.min_audio_length:
                                    print_with_timestamp(f"VAD检测到语音结束，录音时长: {self.total_audio_length:.2f}秒")
                                    
                                    if len(self.audio_buffer) > 0:
                                        yield self.audio_buffer, True
                                    else:
                                        yield np.array([]), True
                                    break
                                else:
                                    print_with_timestamp("录音时长太短，继续录音...")
                                    self.last_sound_time = time.time()
                                    self.non_speech_frames_count = 0
                            else:
                                # 短暂静音，继续录音
                                if len(self.audio_buffer) > 0:
                                    yield self.audio_buffer, False
                                    self.audio_buffer = np.array([], dtype=np.float32)
                    
                    # 短暂休眠以降低CPU使用率
                    await asyncio.sleep(0.01)
                    
                except Exception as e:
                    print_with_timestamp(f"读取音频错误: {e}")
                    error_counter += 1
                    
                    if error_counter >= max_errors:
                        print_with_timestamp(f"连续出现{max_errors}次错误，执行深度重置...")
                        self.deep_reset()
                        error_counter = 0
                    else:
                        # 尝试重新确保设备就绪
                        self.ensure_device_ready()
                        
                    # 添加短暂休眠
                    await asyncio.sleep(0.1)
                    continue

        finally:
            self.is_recording = False
            print_with_timestamp("录音循环已结束")
            # 确保资源释放
            gc.collect()

    def stop_recording(self):
        """停止录音"""
        print_with_timestamp(f"正在停止录音... (当前状态: is_recording={self.is_recording}, recording_started={self.recording_started})")
        self.is_recording = False
        self.recording_started = False
        self.should_stop = True  # 设置停止标志
        print_with_timestamp("录音标志已重置")
        # 清空缓冲区
        self.audio_buffer = np.array([], dtype=np.float32)
        self.vad_buffer = []
        # 等待一小段时间确保录音完全停止
        time.sleep(0.1)
        print_with_timestamp("录音已停止")
        # 强制垃圾回收
        gc.collect()


def load_wake_words() -> List[str]:
    try:
        with open('wake_words.txt', 'r', encoding='utf-8') as f:
            # 只加载少量唤醒词以提高效率
            words = [line.strip() for line in f.readlines() if line.strip()]
            # 如果唤醒词太多，只保留前5个
            if len(words) > 5:
                logger.warning(f"唤醒词过多，仅使用前5个: {words[:5]}")
                return words[:5]
            return words
    except FileNotFoundError:
        # 减少默认唤醒词数量
        return ["你好", "哪吒", "在吗"]


async def call_stt_api_async(audio_data):
    url = STT_URL

    try:
        # 确保音频数据是 numpy 数组
        if not isinstance(audio_data, np.ndarray):
            print(f"警告：音频数据类型不正确，当前类型: {type(audio_data)}")
            audio_data = np.array(audio_data)

        # 确保音频数据是一维数组
        if len(audio_data.shape) > 1:
            audio_data = audio_data.flatten()
            
        # 音频数据下采样，减少发送的数据量
        audio_data = audio_data[::2]  # 每隔一个样本取一个，减少一半数据量

        # 简化日志输出，减少CPU和内存消耗
        print(f"发送音频数据，长度={len(audio_data)}")

        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
            async with session.post(url, json={"audio_data": audio_data.tolist()}) as response:
                if response.status == 200:
                    result = await response.json()
                    transcription = result.get("transcription", "")
                    print(f"STT 服务返回结果: {transcription}")
                    return transcription
                else:
                    print(f"STT API 错误: 状态码={response.status}")
                    return ""
    except asyncio.TimeoutError:
        print("STT API 请求超时")
        return ""
    except Exception as e:
        print(f"调用 STT API 错误: {e}")
        return ""

async def wake_word_detection():
    """唤醒词检测"""
    recorder = get_recorder()
    wake_words = load_wake_words()
    current_text = ""
    is_processing = False
    detection_attempt_count = 0  # 添加检测尝试计数
    max_detection_attempts = 10  # 最大连续检测尝试次数

    try:
        print_with_timestamp("准备使用VAD检测唤醒词...")
        while True:
            # 检查是否需要重置
            recorder.check_reset_needed()
            
            # 检查连续尝试次数是否过多
            detection_attempt_count += 1
            if detection_attempt_count > max_detection_attempts:
                print_with_timestamp(f"连续检测尝试次数过多({detection_attempt_count})，执行深度重置...")
                recorder.deep_reset()
                detection_attempt_count = 0
                await asyncio.sleep(0.5)

            if is_processing:
                await asyncio.sleep(0.2)  # 增加休眠时间
                continue

            recorder.reset()
            is_processing = True

            try:
                async for audio_chunk, timeout in recorder.start_continuous_recording():
                    # 在循环中也检查是否需要重置
                    if recorder.check_reset_needed():
                        print_with_timestamp("执行定时重置，重新开始检测...")
                        break

                    # 检查是否应该停止
                    if recorder.should_stop:
                        print_with_timestamp("检测到停止信号，终止STT处理")
                        break

                    if timeout:
                        print_with_timestamp("VAD检测到语音结束，重新开始检测唤醒词...")
                        break

                    if len(audio_chunk) == 0:
                        continue

                    # 如果设置了停止标志，不再调用STT服务
                    if recorder.should_stop:
                        break

                    # 降低发送到STT服务的频率
                    if len(audio_chunk) < 8000:  # 如果音频太短，跳过此次处理
                        continue

                    # 在调用STT之前检查一次状态
                    if not recorder.is_recording or recorder.should_stop:
                        break
                        
                    transcription = await call_stt_api_async(audio_chunk)
                    
                    # 再次检查停止标志和重置需求
                    if recorder.should_stop or recorder.check_reset_needed():
                        break

                    if transcription.strip():
                        current_text = transcription
                        print_with_timestamp(f"当前识别: '{current_text}'")
                        # 检测到有效文本，重置尝试计数
                        detection_attempt_count = 0

                        for wake_word in wake_words:
                            if wake_word in current_text:
                                print_with_timestamp(f"检测到唤醒词: {wake_word}")
                                # 更新最后重置时间，因为检测到了唤醒词
                                recorder.last_reset_time = time.time()
                                recorder.stop_recording()
                                return {
                                    "status": "success",
                                    "wake_word_detected": True,
                                    "word": wake_word,
                                    "transcription": current_text
                                }

                    # 每次处理后休眠一小段时间，减少CPU使用率
                    await asyncio.sleep(0.1)
            finally:
                is_processing = False
                recorder.stop_recording()
                # 强制垃圾回收
                gc.collect()

            # 如果设置了停止标志，退出主循环
            if recorder.should_stop:
                break
                
            # 每次检测循环后添加休眠
            await asyncio.sleep(0.2)

    except Exception as e:
        print_with_timestamp(f"唤醒词检测错误: {e}")
        recorder.stop_recording()
        # 出现异常，执行深度重置
        recorder.deep_reset()
        raise HTTPException(status_code=500, detail=str(e))

    return {
        "status": "success",
        "wake_word_detected": False,
        "word": "",
        "transcription": current_text
    }

@app.post("/wake")
async def listen_for_wake_word():
    """唤醒接口"""
    try:
        result = await wake_word_detection()
        if result["wake_word_detected"]:
            # 检测到唤醒词后，停止录音并等待新的请求
            recorder = get_recorder()
            recorder.stop_recording()
        return result
    except Exception as e:
        print(f"处理唤醒请求错误: {e}")
        # 出现异常时，尝试深度重置录音设备
        recorder = get_recorder()
        recorder.deep_reset()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/stop_recording")
async def stop_recording():
    """停止当前录音但不关闭服务"""
    try:
        if global_recorder:
            global_recorder.stop_recording()  # 停止录音
            await asyncio.sleep(0.2)  # 等待录音完全停止
            global_recorder.reset()  # 重置状态
            print_with_timestamp("录音已完全停止并重置")
        return {"status": "success", "message": "录音已停止"}
    except Exception as e:
        print_with_timestamp(f"停止录音失败: {e}")
        # 出现异常，执行深度重置
        if global_recorder:
            global_recorder.deep_reset()
        raise HTTPException(status_code=500, detail=str(e))

# 添加重置录音设备的接口
@app.post("/reset_device")
async def reset_device():
    """强制重置录音设备"""
    try:
        recorder = get_recorder()
        recorder.deep_reset()
        return {"status": "success", "message": "录音设备已重置"}
    except Exception as e:
        print_with_timestamp(f"重置录音设备失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 添加VAD配置接口
@app.post("/config_vad")
async def config_vad(vad_mode: int = 3, 
                     speech_frames_threshold: int = None, 
                     non_speech_frames_threshold: int = None):
    """配置VAD参数"""
    try:
        recorder = get_recorder()
        
        # 设置VAD模式 (0-3)
        if 0 <= vad_mode <= 3:
            recorder.vad.set_mode(vad_mode)
            print_with_timestamp(f"VAD模式已设置为: {vad_mode}")
            
        # 设置语音检测阈值
        if speech_frames_threshold is not None and speech_frames_threshold > 0:
            recorder.speech_frames_threshold = speech_frames_threshold
            print_with_timestamp(f"语音帧阈值已设置为: {speech_frames_threshold}")
            
        # 设置非语音检测阈值
        if non_speech_frames_threshold is not None and non_speech_frames_threshold > 0:
            recorder.non_speech_frames_threshold = non_speech_frames_threshold
            print_with_timestamp(f"非语音帧阈值已设置为: {non_speech_frames_threshold}")
            
        return {
            "status": "success", 
            "config": {
                "vad_mode": vad_mode,
                "speech_frames_threshold": recorder.speech_frames_threshold,
                "non_speech_frames_threshold": recorder.non_speech_frames_threshold
            }
        }
    except Exception as e:
        print_with_timestamp(f"配置VAD失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "wake_word_service_vad:app",
        host="0.0.0.0",
        port=8200,
        log_level="info",
        workers=1,  # 减少worker数量
        loop="asyncio"  # 使用asyncio事件循环
    )