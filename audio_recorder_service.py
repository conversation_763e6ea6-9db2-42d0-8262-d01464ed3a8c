import sys
import requests
import uvicorn
from fastapi import FastAPI, HTTPException
import pyaudio
import numpy as np
import time
import socket
import yaml
from loguru import logger

app = FastAPI()


format_string = "[{time:HH:mm:ss.SSS}] {level: <8} | {file}:{line} - {message}"

logger.remove()  # 删除默认处理器
logger.add(sys.stderr, format=format_string)
logger.add(
    "logs/file_{time}.log", 
    rotation="50 MB", 
    retention="10 days", 
    format=format_string,
    enqueue=True  # 使用队列来减少IO阻塞
)


def load_config(config_path="./config/config.yaml"):
    """加载YAML配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
            logger.info(f"成功加载配置文件: {config_path}")
            return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        logger.warning("将使用默认配置")
        return None

# 全局配置
CONFIG = load_config()
# 如果配置加载失败，使用默认值
if CONFIG is None:
    CONFIG = {
        "api": {
            "stt": {
                "url": "http://192.168.20.181:8100/stt/"
            }
        },
        "audio": {
            "threshold_mode": "fixed",   # 默认使用固定阈值模式
            "fixed_threshold": 10000     # 默认固定阈值
        }
    }
elif "audio" not in CONFIG:
    # 如果配置中没有音频相关设置，添加默认值
    CONFIG["audio"] = {
        "threshold_mode": "fixed",
        "fixed_threshold": 10000
    }

STT_URL = CONFIG["api"]["stt"]["ws_url"]
# 音频配置
THRESHOLD_MODE = CONFIG["audio"].get("threshold_mode", "fixed")
FIXED_THRESHOLD = CONFIG["audio"].get("fixed_threshold", 10000)
logger.info(f"THRESHOLD_MODE: {THRESHOLD_MODE}, FIXED_THRESHOLD: {FIXED_THRESHOLD}")

class AudioRecorder:
    def __init__(self):
        self.chunk = 1600
        self.format = pyaudio.paInt16
        self.channels = 1
        self.rate = 16000
        self.threshold = FIXED_THRESHOLD if THRESHOLD_MODE == "fixed" else 5000  # 根据配置设置初始阈值
        self.volume_drop_threshold = 0.3
        self.end_frames_threshold = 30  # 修改为30帧，相当于约3秒的静音
        self.p = None
        self.stream = None
        self.is_recording = False
        self.noise_threshold = None  # 环境噪音阈值

    def start(self):
        """启动录音设备"""
        try:
            if self.p is None:
                self.p = pyaudio.PyAudio()
            if self.stream is None or not self.stream.is_active():
                self.stream = self.p.open(
                    format=self.format,
                    channels=self.channels,
                    rate=self.rate,
                    input=True,
                    frames_per_buffer=self.chunk
                )
                logger.info("录音设备已启动")
        except Exception as e:
            logger.error(f"启动录音设备失败: {e}")
            self.stop()
            raise

    def stop(self):
        """停止录音设备"""
        try:
            if self.stream:
                if self.stream.is_active():
                    self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            if self.p:
                self.p.terminate()
                self.p = None
            logger.info("录音设备已停止")
        except Exception as e:
            logger.error(f"停止录音设备失败: {e}")

    def ensure_device_ready(self):
        """确保录音设备就绪"""
        if self.stream is None or not self.stream.is_active():
            self.start()

    def collect_ambient_noise(self, duration=1):
        """收集环境噪音"""
        self.ensure_device_ready()
        logger.info("\n收集环境噪音中...")

        noise_samples = []
        frames_to_collect = int(self.rate * duration / self.chunk)

        for _ in range(frames_to_collect):
            try:
                data = self.stream.read(self.chunk, exception_on_overflow=False)
                audio_data = np.frombuffer(data, dtype=np.int16)
                current_volume = float(np.max(np.abs(audio_data)))
                noise_samples.append(current_volume)
            except Exception as e:
                logger.error(f"收集噪音样本时出错: {e}")
                continue

        if noise_samples:
            # 使用中位数而不是95百分位数
            noise_level = np.median(noise_samples)
            # 使用更大的倍数，并提高最小阈值
            min_threshold = 6000  # 从5000提高到8000
            max_threshold = 20000  # 从15000提高到20000
            calculated_threshold = noise_level * 2.0  # 从1.5提高到2.0

            # 确保阈值在合理范围内
            self.threshold = max(min_threshold, min(max_threshold, calculated_threshold))

            logger.info(f"环境噪音水平: {noise_level:.0f}")
            logger.info(f"设置录音阈值为: {self.threshold:.0f}")

            # 如果环境噪音异常高，给出警告
            if noise_level > max_threshold:
                logger.warning("警告：环境噪音较大，可能会影响录音质量")
        else:
            logger.info("无法收集环境噪音，使用默认阈值")
            self.threshold = 5000

    def record_by_volume(self):
        """根据音量变化判断录音开始和结束"""
        self.ensure_device_ready()

        # 根据配置决定如何设置阈值
        if THRESHOLD_MODE == "auto":
            # 启用自动噪音检测
            self.collect_ambient_noise()
            if self.threshold > 15000:
                logger.info("等待环境噪音降低...")
                time.sleep(1)
                self.collect_ambient_noise()
        elif THRESHOLD_MODE == "fixed":
            # 使用固定阈值
            self.threshold = FIXED_THRESHOLD
            logger.info(f"使用固定录音阈值: {self.threshold}")
        else:  # "default"
            # 使用默认阈值
            self.threshold = 5000
            logger.info(f"使用默认录音阈值: {self.threshold}")

        frames = []
        is_speaking = False
        low_volume_count = 0
        speaking_volumes = []
        initial_wait = 1.5  # 将初始等待时间设置为1.5秒
        extension_time = 0.5  # 延长时间设置为0.5秒
        current_timeout = initial_wait
        start_time = time.time()
        max_duration = 8  # 初始最大录音时长
        last_volume_check = time.time()
        volume_check_interval = 0.2  # 音量检查间隔
        min_volume_threshold = 5000  # 最小音量阈值
        speech_start_time = None  # 记录开始说话的时间
        min_remaining_time = 1.0  # 确保至少有1秒的剩余时间
        silence_duration = 0.0  # 初始化 silence_duration 变量，这个是静默时间

        try:
            logger.info(f"准备录音，初始等待{initial_wait}秒...")

            while True:
                if self.stream is None or not self.stream.is_active():
                    logger.info("录音设备未就绪，重新启动...")
                    self.start()

                data = self.stream.read(self.chunk, exception_on_overflow=False)
                audio_data = np.frombuffer(data, dtype=np.int16)
                current_volume = float(np.max(np.abs(audio_data)))

                elapsed_time = time.time() - start_time
                # 确保remaining_time至少为2秒
                remaining_time = max(min_remaining_time, current_timeout - elapsed_time)
                
                logger.info(f"当前音量: {current_volume:.0f}, 阈值: {self.threshold:.0f}, 剩余时间: {remaining_time:.1f}秒",
                      end='\r')

                # 检测到说话声音
                if current_volume > self.threshold:
                    if not is_speaking:
                        logger.info(f"\n检测到声音，重置计时... (音量: {current_volume:.0f})")
                        is_speaking = True
                        speech_start_time = time.time()  # 记录开始说话的时间
                    # 重置计时器和起始时间
                    start_time = time.time()
                    # 确保超时时间至少为2秒
                    current_timeout = max(extension_time, min_remaining_time)
                    speaking_volumes.append(current_volume)
                    low_volume_count = 0
                    frames.append(data)

                # 已经在录音状态
                elif is_speaking:
                    frames.append(data)
                    low_volume_count += 1

                    # 定期检查是否还在说话
                    current_time = time.time()
                    if current_time - last_volume_check >= volume_check_interval:
                        last_volume_check = current_time
                        # 计算最近一段时间的平均音量
                        recent_volume = np.mean(
                            [float(np.max(np.abs(np.frombuffer(f, dtype=np.int16)))) for f in frames[-5:]])

                        # 如果音量仍然较高，说明可能还在说话
                        if recent_volume > min_volume_threshold:
                            current_duration = len(frames) * self.chunk / self.rate
                            if current_duration >= max_duration - 1:  # 快到达最大时长
                                max_duration += 2  # 延长2秒
                                logger.info(f"\n检测到正在说话，延长录音时间到 {max_duration} 秒")
                                low_volume_count = 0  # 重置静音计数
                        # 如果检测到当前音量超过阈值的70%，也重置计时
                        elif current_volume > self.threshold * 0.7:
                            start_time = time.time()  # 重置计时器
                            low_volume_count = 0  # 重置低音量计数
                            # 确保超时时间至少为2秒
                            current_timeout = max(extension_time, min_remaining_time)

                    # 计算当前帧数对应的秒数
                    silence_duration = low_volume_count * self.chunk / self.rate
                    if silence_duration >= 1.0:  # 静音持续2秒后结束录音
                        logger.info(f"\n检测到说话结束，静音持续了 {silence_duration:.1f} 秒")
                        break

                # 检查是否达到当前超时时间 - 只有在真正超过超时时间且至少等待2秒时才结束
                if elapsed_time >= current_timeout and elapsed_time >= min_remaining_time:
                    if not is_speaking:
                        logger.info(f"\n已等待 {elapsed_time:.1f} 秒，未检测到声音，录音结束")
                        return None
                    elif silence_duration >= 1.0:  # 降低超时检查的静音时间要求为1秒
                        logger.info(f"\n达到超时时间且检测到静音 {silence_duration:.1f} 秒，录音结束")
                        break
                    else:
                        # 如果还没到静音时间但超时了，延长一些时间
                        start_time = time.time()
                        # 确保延长的超时时间至少为2秒
                        current_timeout = max(2.0, extension_time)
                        logger.info(f"\n延长录音时间，新的剩余时间: {current_timeout:.1f}秒")

                # 检查是否达到最大录音时长
                if len(frames) * self.chunk / self.rate > max_duration:
                    logger.info(f"\n达到最大录音时长 {max_duration} 秒，结束录音")
                    break

            if not frames or len(frames) < 8:  # 保至少有一定长度的录音
                logger.info("\n未检测到有效语音")
                return None

            # 处理音频数据
            wav_data = np.frombuffer(b''.join(frames), dtype=np.int16).astype(np.float32)
            wav_data /= 32768.0

            return wav_data

        except Exception as e:
            logger.error(f"\n录音错误: {e}")
            return None
        finally:
            self.is_recording = False


def call_stt_api(audio_data):
    """调用语音识别服务"""

    url = STT_URL
    response = requests.post(url, json={"audio_data": audio_data.tolist()})
    if response.status_code == 200:
        logger.info("Transcription:", response.json()["transcription"])
        return response.json()["transcription"]
    return ""


def listen_and_save():
    """录音并返回处理后的数据"""
    try:
        recorder = AudioRecorder()
        recorder.start()  # 确保设备启动
        audio_data = recorder.record_by_volume()

        if audio_data is None:
            return {
                "status": "timeout",
                "message": "未检测到语音",
                "audio_data": [],
                "transcription": ""
            }

        logger.info("开始转录...")
        transcription = call_stt_api(audio_data)
        logger.info(f"转录完成: {transcription}")

        return {
            "status": "success",
            "message": "录音完成",
            "audio_data": audio_data.tolist(),
            "transcription": transcription
        }
    except Exception as e:
        logger.error(f"录音或转录错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/record")
def record_audio():
    """录音接口"""
    try:
        return listen_and_save()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/prepare")
def prepare_service():
    """准备录音设备"""
    recorder = AudioRecorder()
    return {"status": "ready"}


def is_port_in_use(port: int) -> bool:
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('0.0.0.0', port))
            return False
        except OSError:
            return True


def find_available_port(start_port: int, max_attempts: int = 10) -> int:
    port = start_port
    while port < start_port + max_attempts:
        if not is_port_in_use(port):
            return port
        port += 1
    raise RuntimeError(f"No available ports found between {start_port} and {start_port + max_attempts - 1}")


if __name__ == "__main__":
    try:
        port = 8400
        if is_port_in_use(port):
            logger.info(f"端口 {port} 已被占用，尝试关闭之前的进程...")
            # 在 Windows 上使用 taskkill 命令
            import os

            os.system(f'taskkill /F /PID {os.getpid()}')
            time.sleep(1)  # 等待进程完全关闭

            if is_port_in_use(port):
                port = find_available_port(8301)
                logger.info(f"使用新端口: {port}")

        uvicorn.run(
            "audio_recorder_service:app",
            host="0.0.0.0",
            port=port,
            log_level="info"
        )
    except Exception as e:
        logger.error(f"启动服务失败: {e}")