import json
import sys
import os
import time
import asyncio
import aiohttp
import random
import glob
import yaml
from loguru import logger
from new_code.doubao_tts_service import synthesize_speech

# 配置loguru
format_string = "[{time:HH:mm:ss.SSS}] {level: <8} | {file}:{line} - {message}"

logger.remove()  # 删除默认处理器
logger.add(sys.stderr, format=format_string)
logger.add(
    "logs/stream_{time}.log", 
    rotation="50 MB", 
    retention="10 days", 
    format=format_string
)

# 加载配置文件
def load_config(config_path="./config/config.yaml"):
    """加载YAML配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
            logger.info(f"成功加载配置文件: {config_path}")
            return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return None

# 全局配置
_config = load_config()
if not _config:
    raise Exception("无法加载配置文件")

# 全局变量
_api_key = _config["api"]["doubao"]["api_key"]
_model = _config["api"]["doubao"]["model"]
_api_url = _config["api"]["doubao"]["url"]
_yuyin_folder = _config.get("idle_audio", {}).get("directory", "yuyin")
_random_response_threshold = _config.get("conversation", {}).get("random_response_threshold", 70)

# 动作播放相关导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from audio_action_controller import play_audio_with_action, play_idle_audio_with_action
except ImportError:
    logger.error("无法导入 audio_action_controller，将使用默认播放方式")
    # 定义替代函数
    def play_audio_with_action(audio_path):
        logger.warning(f"使用默认方式播放音频: {audio_path}")
        return True
    
    def play_idle_audio_with_action(audio_path):
        logger.warning(f"使用默认方式播放闲置音频: {audio_path}")
        return True

# 添加对话历史存储
_conversation_history = [
    {
        "content": _config.get("system_prompt", {}).get("text", "你是一个有用的AI助手，你会用简短、友好的方式回答问题。你的回答要准确、简洁。"),
        "role": "system"
    }
]

def _get_random_yuyin_file():
    """随机获取yuyin文件夹中的一个mp3文件"""
    global _yuyin_folder

    try:
        # 确保使用绝对路径
        yuyin_dir = _yuyin_folder
        if not os.path.isabs(yuyin_dir):
            # 获取当前工作目录
            current_dir = os.getcwd()
            yuyin_dir = os.path.join(current_dir, yuyin_dir)
            logger.warning(f"【随机播放】当前工作目录: {current_dir}")

        logger.warning(f"【随机播放】搜索目录: {yuyin_dir}")

        # 检查目录是否存在
        if not os.path.exists(yuyin_dir):
            logger.warning(f"【随机播放】目录不存在: {yuyin_dir}")
            # 尝试在上级目录查找
            parent_dir = os.path.dirname(current_dir)
            yuyin_dir = os.path.join(parent_dir, _yuyin_folder)
            logger.warning(f"【随机播放】尝试在上级目录查找: {yuyin_dir}")
            
            if not os.path.exists(yuyin_dir):
                logger.warning(f"【随机播放】在上级目录中也未找到yuyin文件夹")
                return None

        # 列出目录下的所有文件，用于调试
        try:
            all_files = os.listdir(yuyin_dir)
            logger.warning(f"【随机播放】目录中的所有文件: {all_files}")
        except Exception as e:
            logger.error(f"【随机播放】列出目录内容失败: {e}")
            all_files = []

        # 获取yuyin文件夹中所有mp3文件
        mp3_pattern = os.path.join(yuyin_dir, "*.mp3")
        mp3_files = glob.glob(mp3_pattern)

        logger.warning(f"【随机播放】查找模式 '{mp3_pattern}' 找到 {len(mp3_files)} 个MP3文件")

        if not mp3_files:
            logger.warning(f"【随机播放】在 {yuyin_dir} 文件夹中未找到MP3文件")
            return None

        # 随机选择一个mp3文件
        random_mp3 = random.choice(mp3_files)
        logger.warning(f"【随机播放】随机选择yuyin文件: {random_mp3}")
        print(f"\n===== 【随机播放】随机选择yuyin文件: {random_mp3} =====\n")

        return random_mp3
    except Exception as e:
        logger.error(f"【随机播放】获取随机yuyin文件时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

async def play_random_audio():
    """播放随机语音"""
    random_mp3 = _get_random_yuyin_file()

    if random_mp3:
        # 打印选择的文件名，便于调试
        file_name = os.path.basename(random_mp3)
        logger.warning(f"【随机播放】选择的语音文件: {file_name}")

        # 检查是否存在对应的动作文件
        action_file = random_mp3.rsplit('.', 1)[0] + '_action.yaml'
        if os.path.exists(action_file):
            logger.warning(f"【随机播放】找到对应的动作文件: {os.path.basename(action_file)}")
        else:
            logger.warning(f"【随机播放】未找到动作文件: {os.path.basename(action_file)}，将使用默认动作")

        # 播放随机选择的MP3文件（使用带动作控制的播放函数）
        logger.warning(f"【随机播放】播放音频和动作: {file_name}")

        success = play_idle_audio_with_action(random_mp3)
        if not success:
            logger.warning(f"【随机播放】语音 {file_name} 播放失败")
            return False
        else:
            logger.warning(f"【随机播放】语音 {file_name} 播放成功")
            return True
    else:
        logger.warning("【随机播放】未能获取随机语音文件")
        return False

async def synthesize_startup_prompt(text):
    """直接合成启动提示音，不经过对话系统"""
    try:
        synthesize_speech(text)
        return True
    except Exception as e:
        logger.error(f"合成启动提示音失败: {e}")
        return False

async def stream_chat(prompt="你好", keep_history=True, direct_synthesis=False):
    """使用豆包API处理流式文本请求"""
    global _conversation_history, _random_response_threshold
    
    # 如果是直接合成模式，跳过对话系统
    if direct_synthesis:
        return await synthesize_startup_prompt(prompt)
    
    # 添加用户问题到对话历史
    _conversation_history.append({
        "content": prompt,
        "role": "user"
    })

    # 如果对话历史太长，保留最近的5轮对话
    if len(_conversation_history) > 11:  # system提示+5轮对话(10条消息)
        _conversation_history = [_conversation_history[0]] + _conversation_history[-10:]
    
    # 生成一个0-100之间的随机数来决定是AI回复还是随机吼叫
    rand_value = random.randint(0, 100)
    decision = "使用AI回复" if rand_value < _random_response_threshold else "播放随机语音"
    logger.warning(f"【随机决策】生成随机数: {rand_value} —— 阈值: {_random_response_threshold} —— {decision}")
    print(f"\n========= 【随机决策】生成随机数: {rand_value} —— 阈值: {_random_response_threshold} —— {decision} =========\n")
    
    # 如果随机数小于阈值，使用AI回复；否则播放随机音频
    if rand_value < _random_response_threshold:
        # 使用AI回复
        return await _process_ai_response(prompt, keep_history)
    else:
        # 播放随机音频
        success = await play_random_audio()
        if success:
            # 由于没有AI回复，所以不添加到对话历史
            # 但仍保留上下文，以便下次继续对话
            return True
        else:
            # 如果随机播放失败，回退到AI回复
            logger.warning("随机播放失败，回退到AI回复")
            return await _process_ai_response(prompt, keep_history)

async def _process_ai_response(prompt, keep_history=True):
    """处理AI回复流程"""
    global _conversation_history, _api_key, _model, _api_url
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {_api_key}",
        "X-Request-Source": "sdk"
    }

    # 使用完整的对话历史创建payload
    payload_dict = {
        "messages": _conversation_history,
        "model": _model,
        "stream": True
    }

    # 转换为JSON字符串，确保布尔值正确转换
    payload = json.dumps(payload_dict, ensure_ascii=False)
    logger.info(f"发送请求体: {payload}")

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(_api_url, data=payload, headers=headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"API请求失败，状态码: {response.status}")
                    logger.error(f"错误详情: {error_text}")
                    return False

                current_sentence = ""
                complete_text = ""  # 用于存储完整的回答文本
                # 处理流式响应
                async for line in response.content:
                    if line:
                        try:
                            line = line.decode('utf-8').strip()
                            if line.startswith('data: ') and line != 'data: [DONE]':
                                json_str = line[6:]  # 移除 "data: " 前缀
                                data = json.loads(json_str)

                                if (data.get('choices') and
                                    len(data['choices']) > 0 and
                                    data['choices'][0].get('delta') and
                                    data['choices'][0]['delta'].get('content')):

                                    content = data['choices'][0]['delta']['content']
                                    current_sentence += content
                                    complete_text += content  # 累积完整文本

                                    # 检查是否包含标点符号（。！？，；）
                                    for punct in ['。', '！', '？', '，', '；']:
                                        if punct in current_sentence:
                                            sentences = current_sentence.split(punct)
                                            # 打印除最后一个部分外的所有完整句子
                                            for sentence in sentences[:-1]:
                                                if sentence.strip():
                                                    print(sentence.strip() + punct)
                                                    logger.info(f"输出句子: {sentence.strip() + punct}")
                                            # 保留最后一个可能不完整的部分
                                            current_sentence = sentences[-1]

                        except json.JSONDecodeError as e:
                            logger.error(f"JSON解析错误: {e}")
                        except Exception as e:
                            logger.error(f"处理响应时发生错误: {e}")

                # 打印最后剩余的内容
                if current_sentence.strip():
                    print(current_sentence.strip())
                    logger.info(f"输出最后的句子: {current_sentence.strip()}")
                
                # 对完整的回答文本进行语音合成
                if complete_text.strip():
                    logger.info("开始进行语音合成...")
                    try:
                        synthesize_speech(complete_text.strip())
                        logger.info("语音合成完成")
                    except Exception as e:
                        logger.error(f"语音合成失败: {e}")
                    
                # 将AI的回答添加到对话历史
                if complete_text.strip() and keep_history:
                    _conversation_history.append({
                        "content": complete_text.strip(),
                        "role": "assistant"
                    })
                return True
                    
    except aiohttp.ClientError as e:
        logger.error(f"请求发生错误: {e}")
        return False
    except Exception as e:
        logger.error(f"发生未知错误: {e}")
        return False

async def clear_history():
    """清除对话历史"""
    global _conversation_history
    _conversation_history = [_conversation_history[0]]  # 只保留system提示
    logger.info("对话历史已清除")
    return True

def set_random_threshold(threshold):
    """设置随机响应阈值"""
    global _random_response_threshold
    if 0 <= threshold <= 100:
        _random_response_threshold = threshold
        logger.info(f"已设置随机响应阈值为: {threshold}")
        return True
    else:
        logger.warning(f"无效的阈值 {threshold}，需要在0-100之间")
        return False

async def main():
    """主函数"""
    logger.info("开始运行流式对话")
    await stream_chat()

if __name__ == "__main__":
    asyncio.run(main()) 