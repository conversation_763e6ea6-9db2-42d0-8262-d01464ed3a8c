# -*- coding: utf-8 -*-
"""
状态管理模块
用于管理系统的各种状态
"""

import threading
import sys
# from loguru import logger

from logger_config import setup_logger

# 设置日志
logger = setup_logger()

# 配置loguru
# format_string = "[{time:HH:mm:ss.SSS}] {level: <8} | {file}:{line} - {message}"
#
# logger.remove()  # 删除默认处理器
# logger.add(sys.stderr, format=format_string)
# logger.add(
#     "logs/file_{time}.log",
#     rotation="50 MB",
#     retention="10 days",
#     format=format_string
# )

# 全局状态变量
_STATE = {
    "in_conversation": False,  # 是否在对话中
    "in_mq_state": False,     # 是否在MQ状态
    "is_playing_idle": False, # 是否在播放闲置音频
    "is_processing": False,   # 是否正在处理
}

def initialize():
    """初始化状态"""
    global _STATE
    _STATE = {
        "in_conversation": False,
        "in_mq_state": False,
        "is_playing_idle": False,
        "is_processing": False,
    }

def get_state_info():
    """获取当前状态信息"""
    return _STATE.copy()

def _check_state_conflict(new_state_key, new_value):
    """检查状态切换是否会造成冲突"""
    if not new_value:  # 如果是要关闭某个状态，不需要检查冲突
        return False
        
    # 定义互斥状态组
    exclusive_states = {
        "in_conversation": ["in_mq_state", "is_playing_idle"],
        "in_mq_state": ["in_conversation", "is_playing_idle"],
        "is_playing_idle": ["in_conversation", "in_mq_state"]
    }
    
    # 检查是否有冲突
    if new_state_key in exclusive_states:
        for conflicting_state in exclusive_states[new_state_key]:
            if _STATE[conflicting_state]:
                logger.warning(f"状态冲突: 不能设置 {new_state_key}=True，因为 {conflicting_state}=True")
                return True
    return False

def _set_state(state_key, value):
    """设置状态的通用函数"""
    global _STATE
    if _STATE[state_key] != value:
        # 检查状态冲突
        if _check_state_conflict(state_key, value):
            return False
            
        logger.info(f"{state_key} 状态切换: {_STATE[state_key]} -> {value}")
        _STATE[state_key] = value
        
        # 如果设置了任何活动状态，同时设置处理状态
        if value and state_key != "is_processing":
            _STATE["is_processing"] = True
            logger.info(f"处理状态切换: False -> True")
        
        # 如果关闭了所有活动状态，清除处理状态
        if not value and not any(v for k, v in _STATE.items() if k != "is_processing"):
            _STATE["is_processing"] = False
            logger.info(f"处理状态切换: True -> False")
            
        return True
    return False

def set_conversation_state(value):
    """设置对话状态"""
    return _set_state("in_conversation", value)

def set_mq_state(value):
    """设置MQ状态"""
    return _set_state("in_mq_state", value)

def set_idle_audio_state(value):
    """设置闲置音频状态"""
    return _set_state("is_playing_idle", value)

def is_in_conversation():
    """是否在对话中"""
    return _STATE["in_conversation"]

def is_in_mq_state():
    """是否在MQ状态"""
    return _STATE["in_mq_state"]

def is_playing_idle():
    """是否在播放闲置音频"""
    return _STATE["is_playing_idle"]

def is_processing():
    """是否正在处理中"""
    return _STATE["is_processing"]

def can_enter_mq_state():
    """是否可以进入MQ状态"""
    return not any(_STATE.values())

def can_continue_conversation():
    """是否可以继续对话"""
    # 只有当前在对话中且不在处理状态时，才能继续对话
    return _STATE["in_conversation"] and not _STATE["is_processing"] and not _STATE["is_playing_idle"] and not _STATE["in_mq_state"]

def set_processing(state):
    """设置处理状态"""
    global _STATE
    _STATE["is_processing"] = state
    if _STATE["is_processing"] != state:
        logger.info(f"处理状态切换: {_STATE['is_processing']} -> {state}")

def set_playing_idle(state):
    """设置闲置音频播放状态"""
    global _STATE
    _STATE["is_playing_idle"] = state
    if _STATE["is_playing_idle"] != state:
        logger.info(f"闲置音频播放状态切换: {_STATE['is_playing_idle']} -> {state}")

def set_conversation(state):
    """设置对话状态"""
    global _STATE
    _STATE["in_conversation"] = state
    if _STATE["in_conversation"] != state:
        logger.info(f"对话状态切换: {_STATE['in_conversation']} -> {state}")
        
def is_busy():
    """检查系统是否忙碌"""
    return _STATE["is_processing"]
        
def can_start_conversation():
    """检查是否可以开始对话"""
    return not (_STATE["is_playing_idle"] or _STATE["in_conversation"])
        
def can_play_idle_audio():
    """检查是否可以播放闲置音频"""
    return not (_STATE["is_processing"] or _STATE["in_conversation"] or _STATE["is_playing_idle"] or _STATE["in_mq_state"])
        
def load_wake_words():
    """加载唤醒词"""
    global _STATE
    try:
        with open('wake_words.txt', 'r', encoding='utf-8') as f:
            words = [line.strip() for line in f.readlines() if line.strip()]
            logger.info(f"已加载唤醒词: {words}")
            _STATE["wake_words"] = words
            return words
    except FileNotFoundError:
        logger.warning("未找到wake_words.txt文件，使用默认唤醒词")
        _STATE["wake_words"] = ["你好"]
        return ["你好"]
    except Exception as e:
        logger.error(f"加载唤醒词文件出错: {e}")
        _STATE["wake_words"] = ["你好"]
        return ["你好"]

def get_wake_words():
    """获取唤醒词列表"""
    global _STATE
    if not _STATE["wake_words"]:
        load_wake_words()
    return _STATE["wake_words"]

def set_last_transcription(text):
    """设置上一次的识别结果"""
    global _STATE
    _STATE["last_transcription"] = text

def get_last_transcription():
    """获取上一次的识别结果"""
    return _STATE["last_transcription"]

# 初始化模块
initialize()

if __name__ == "__main__":
    # 测试状态管理
    initialize()
    print(f"初始状态: {get_state_info()}")
    
    set_processing(True)
    print(f"设置处理状态为True: {get_state_info()}")
    
    print(f"是否可以开始对话: {can_start_conversation()}")
    print(f"是否可以播放闲置音频: {can_play_idle_audio()}") 

    #第三种状态
    