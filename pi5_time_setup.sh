#!/bin/bash
# 树莓派Pi5时间设置脚本

echo "=== 树莓派Pi5时间设置向导 ==="

# 检查网络连接
check_network() {
    if ping -c 1 8.8.8.8 &> /dev/null; then
        return 0  # 有网络
    else
        return 1  # 无网络
    fi
}

# 设置有网络环境的时间同步
setup_network_time() {
    echo "检测到网络连接，设置NTP时间同步..."
    
    # 设置时区
    echo "设置时区为Asia/Shanghai..."
    sudo timedatectl set-timezone Asia/Shanghai
    
    # 启用NTP同步
    echo "启用NTP时间同步..."
    sudo timedatectl set-ntp true
    
    # 配置NTP服务器
    echo "配置中国NTP服务器..."
    sudo tee /etc/systemd/timesyncd.conf > /dev/null << EOF
[Time]
NTP=ntp.aliyun.com ntp1.aliyun.com time.nist.gov
FallbackNTP=pool.ntp.org cn.pool.ntp.org
RootDistanceMaxSec=5
PollIntervalMinSec=32
PollIntervalMaxSec=2048
EOF
    
    # 重启时间同步服务
    sudo systemctl restart systemd-timesyncd
    
    # 等待同步
    echo "等待时间同步..."
    sleep 5
    
    # 显示同步状态
    echo "时间同步状态:"
    timedatectl status
    
    echo "网络时间同步设置完成！"
}

# 设置无网络环境的时间管理
setup_offline_time() {
    echo "未检测到网络连接，设置离线时间管理..."
    
    # 禁用NTP同步
    sudo timedatectl set-ntp false
    
    # 设置时区
    sudo timedatectl set-timezone Asia/Shanghai
    
    # 检查是否存在时间管理脚本
    if [ ! -f "time_manager.py" ]; then
        echo "错误: 未找到time_manager.py文件"
        echo "请确保在项目目录中运行此脚本"
        exit 1
    fi
    
    # 手动设置时间
    echo "请手动设置当前时间:"
    read -p "年份 (例如: 2025): " year
    read -p "月份 (1-12): " month
    read -p "日期 (1-31): " day
    read -p "小时 (0-23): " hour
    read -p "分钟 (0-59): " minute
    
    # 验证输入
    if [[ ! "$year" =~ ^[0-9]{4}$ ]] || [[ ! "$month" =~ ^[0-9]{1,2}$ ]] || 
       [[ ! "$day" =~ ^[0-9]{1,2}$ ]] || [[ ! "$hour" =~ ^[0-9]{1,2}$ ]] || 
       [[ ! "$minute" =~ ^[0-9]{1,2}$ ]]; then
        echo "输入格式错误，请重新运行脚本"
        exit 1
    fi
    
    # 使用Python脚本设置时间
    python3 set_time.py --year $year --month $month --day $day --hour $hour --minute $minute
    
    # 安装离线时间管理服务
    if [ -f "install_time_service.sh" ]; then
        echo "安装离线时间管理服务..."
        sudo ./install_time_service.sh
    else
        echo "警告: 未找到install_time_service.sh，跳过服务安装"
    fi
    
    echo "离线时间管理设置完成！"
}

# Pi5特定优化
pi5_optimizations() {
    echo "应用树莓派Pi5特定优化..."
    
    # 检查是否有RTC模块
    if [ -e /dev/rtc0 ]; then
        echo "检测到RTC设备，配置硬件时钟同步..."
        
        # 确保hwclock服务启用
        sudo systemctl enable systemd-hwclock-save.service
        
        # 同步系统时间到硬件时钟
        sudo hwclock -w
        
        echo "硬件时钟同步完成"
    else
        echo "未检测到RTC设备，使用软件时间管理"
    fi
    
    # 优化时间相关的系统服务
    echo "优化系统时间服务..."
    
    # 确保时间相关服务正常运行
    sudo systemctl enable systemd-timesyncd
    sudo systemctl enable systemd-timedated
    
    # 设置更合理的时间同步间隔（如果使用NTP）
    if systemctl is-active --quiet systemd-timesyncd; then
        echo "NTP服务运行中，时间将自动同步"
    fi
}

# 显示时间状态
show_time_status() {
    echo ""
    echo "=== 当前时间状态 ==="
    echo "系统时间: $(date)"
    echo "硬件时钟: $(sudo hwclock -r 2>/dev/null || echo '无法读取')"
    echo ""
    echo "详细状态:"
    timedatectl status
    echo ""
}

# 主程序
main() {
    # 检查是否以root权限运行某些操作
    if [ "$EUID" -eq 0 ]; then
        echo "请不要以root用户直接运行此脚本"
        echo "脚本会在需要时自动使用sudo"
        exit 1
    fi
    
    # 显示当前状态
    show_time_status
    
    # 检查网络连接
    if check_network; then
        echo "检测到网络连接"
        read -p "是否设置网络时间同步? (Y/n): " choice
        case "$choice" in
            n|N|no|No)
                setup_offline_time
                ;;
            *)
                setup_network_time
                ;;
        esac
    else
        echo "未检测到网络连接"
        setup_offline_time
    fi
    
    # 应用Pi5优化
    pi5_optimizations
    
    # 显示最终状态
    echo ""
    echo "=== 设置完成后的时间状态 ==="
    show_time_status
    
    echo "时间设置完成！"
    echo ""
    echo "建议:"
    echo "1. 重启系统验证时间设置是否持久化"
    echo "2. 如果使用离线模式，定期手动校正时间"
    echo "3. 监控时间漂移情况"
}

# 运行主程序
main "$@"
