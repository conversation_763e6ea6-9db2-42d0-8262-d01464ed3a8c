---
description: 
globs: 
alwaysApply: false
---
# 代码嵌套深度规则

## 循环和条件语句嵌套规则

为了保持代码的可读性和可维护性，我们制定以下规则：

1. `for` 循环和 `if` 语句的嵌套深度不应超过2层
2. 当需要超过2层嵌套时，应将内部逻辑提取为独立的函数
3. 提取的函数应该有清晰的命名，表明其功能
4. 每个函数应该只做一件事，保持单一职责原则

### 示例

✅ 好的做法：
```python
def process_data(data):
    for item in data:
        if item.is_valid:
            process_item(item)

def process_item(item):
    if item.type == 'A':
        handle_type_a(item)
    elif item.type == 'B':
        handle_type_b(item)
```

❌ 避免的做法：
```python
def process_data(data):
    for item in data:
        if item.is_valid:
            for subitem in item.subitems:
                if subitem.type == 'A':
                    # 处理逻辑
                    pass
```

## 依赖版本说明

- pymodbus 版本: 3.9.1

## 代码审查检查点

在代码审查时，请特别注意：
1. 检查嵌套深度是否超过2层
2. 如果发现深层嵌套，建议重构为独立函数
3. 确保提取的函数命名清晰，职责单一

