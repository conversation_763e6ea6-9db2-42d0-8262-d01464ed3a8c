import sys
import uvicorn
from fastapi import FastAPI, HTTPException
import pyaudio
import numpy as np
import aiohttp
import asyncio
from typing import List
from concurrent.futures import ThreadPoolExecutor
import time
from datetime import datetime
import yaml
from loguru import logger

app = FastAPI()
executor = ThreadPoolExecutor(max_workers=1)  # 减少工作线程数量

format_string = "[{time:HH:mm:ss.SSS}] {level: <8} | {file}:{line} - {message}"

logger.remove()  # 删除默认处理器
logger.add(sys.stderr, format=format_string)
logger.add(
    "logs/file_{time}.log", 
    rotation="10 MB",  # 减小日志文件大小
    retention="5 days",  # 减少保留天数
    format=format_string,
    enqueue=True  # 使用队列来减少IO阻塞
)

# 加载配置文件
def load_config(config_path="./config/config.yaml"):
    """加载YAML配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
            logger.info(f"成功加载配置文件: {config_path}")
            return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        logger.warning("将使用默认配置")
        return None

# 全局配置
CONFIG = load_config()
# 如果配置加载失败，使用默认值
if CONFIG is None:
    CONFIG = {
        "api": {
            "stt": {
                "url": "http://192.168.20.181:8100/stt/"
            }
        }
    }
    
STT_URL = CONFIG["api"]["stt"]["url"]
# 添加全局录音器实例
global_recorder = None

def get_recorder():
    global global_recorder
    if global_recorder is None:
        global_recorder = AudioRecorder()
    return global_recorder

def print_with_timestamp(message):
    print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] {message}")


class AudioRecorder:
    def __init__(self):
        self.chunk = 3200  # 增大块大小，减少处理次数
        self.format = pyaudio.paInt16
        self.channels = 1
        self.rate = 16000
        self.threshold = 60  # 提高声音检测阈值，减少误触发
        self.buffer_size = 16000
        self.silence_timeout = 1.2  # 增加静音超时时间
        self.min_audio_length = 0.5  # 最小音频长度（秒）
        self.p = None
        self.stream = None
        self.frames = []
        self.is_recording = False
        self.audio_buffer = np.array([], dtype=np.float32)
        self.last_sound_time = time.time()
        self.recording_started = False
        self.total_audio_length = 0  # 记录总音频长度
        self._recording_lock = asyncio.Lock()  # 添加锁来控制录音状态
        self.should_stop = False  # 添加停止标志
        self.last_reset_time = time.time()  # 添加最后重置时间
        self.reset_interval = 300  # 5分钟 = 300秒
        self.speaking_extension = 0.5  # 说话时延长的时间
        self.max_silence_count = 3  # 连续静音次数阈值
        self.silence_count = 0  # 静音计数器
        self.energy_threshold = 0.05  # 能量阈值，用于更有效的音频检测
        self.sample_rate = 0.4  # 采样率缩减因子，每隔几个样本计算一次，减少CPU负载

    def start(self):
        """启动录音设备"""
        try:
            if self.p is None:
                self.p = pyaudio.PyAudio()
            if self.stream is None or not self.stream.is_active():
                self.stream = self.p.open(
                    format=self.format,
                    channels=self.channels,
                    rate=self.rate,
                    input=True,
                    frames_per_buffer=self.chunk
                )
                print_with_timestamp("录音设备已启动")
        except Exception as e:
            print_with_timestamp(f"启动录音设备失败: {e}")
            self.stop()
            raise

    def stop(self):
        """停止录音设备"""
        try:
            if self.stream:
                if self.stream.is_active():
                    self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            if self.p:
                self.p.terminate()
                self.p = None
            print_with_timestamp("录音设备已停止")
        except Exception as e:
            print_with_timestamp(f"停止录音设备失败: {e}")

    def ensure_device_ready(self):
        """确保录音设备就绪"""
        if self.stream is None or not self.stream.is_active():
            self.start()

    def check_reset_needed(self):
        """检查是否需要重置"""
        current_time = time.time()
        if current_time - self.last_reset_time > self.reset_interval:
            print_with_timestamp("已超过5分钟未唤醒，执行自动重置...")
            self.reset()
            self.last_reset_time = current_time
            return True
        return False

    def reset(self):
        """重置录音状态"""
        print_with_timestamp("重置录音状态...")
        self.frames = []
        self.is_recording = False
        self.recording_started = False
        self.should_stop = False
        self.audio_buffer = np.array([], dtype=np.float32)
        self.last_sound_time = time.time()
        self.total_audio_length = 0
        self.silence_count = 0
        print_with_timestamp("录音状态已重置")

    def calculate_energy(self, audio_data):
        """计算音频能量，只使用部分采样点以降低CPU使用率"""
        # 仅使用部分采样点计算能量
        sampled_data = audio_data[::int(1/self.sample_rate)]
        if len(sampled_data) == 0:
            return 0
        return np.mean(np.abs(sampled_data))

    async def start_continuous_recording(self):
        """开始持续录音"""
        self.reset()  # 重置状态
        self.is_recording = True
        self.silence_count = 0  # 重置静音计数器

        try:
            self.ensure_device_ready()
            
            # 降低CPU使用率的计数器
            processing_counter = 0
            
            while self.is_recording:
                try:
                    data = self.stream.read(self.chunk, exception_on_overflow=False)
                    audio_data = np.frombuffer(data, dtype=np.int16)
                    
                    # 每两次读取处理一次，降低CPU使用率
                    processing_counter += 1
                    if processing_counter % 2 != 0:
                        # 仍然保留数据，但不处理
                        current_audio = audio_data.astype(np.float32) / 32768.0
                        self.audio_buffer = np.append(self.audio_buffer, current_audio)
                        await asyncio.sleep(0.01)
                        continue
                    
                    # 使用更高效的能量计算替代max(abs())
                    current_energy = self.calculate_energy(audio_data)

                    if current_energy > self.energy_threshold:
                        if not self.recording_started:
                            print_with_timestamp("检测到声音，开始录音...")
                            self.recording_started = True

                        # 重置静音计数器，因为检测到声音
                        self.silence_count = 0
                        self.last_sound_time = time.time()
                        current_audio = audio_data.astype(np.float32) / 32768.0
                        self.audio_buffer = np.append(self.audio_buffer, current_audio)
                        self.total_audio_length += len(current_audio) / self.rate

                        if len(self.audio_buffer) >= self.buffer_size:
                            yield self.audio_buffer[:self.buffer_size], False
                            self.audio_buffer = self.audio_buffer[self.buffer_size:]
                    elif self.recording_started:
                        current_audio = audio_data.astype(np.float32) / 32768.0
                        self.audio_buffer = np.append(self.audio_buffer, current_audio)

                        # 计算静音时间
                        silence_duration = time.time() - self.last_sound_time
                        
                        # 如果超过静音超时时间
                        if silence_duration > self.silence_timeout:
                            self.silence_count += 1
                            
                            if self.silence_count >= self.max_silence_count:
                                if self.total_audio_length >= self.min_audio_length:
                                    print_with_timestamp(f"检测到连续静音，录音时长: {self.total_audio_length:.2f}秒")
                                    if len(self.audio_buffer) > 0:
                                        yield self.audio_buffer, True
                                    else:
                                        yield np.array([]), True
                                    break
                                else:
                                    print_with_timestamp("录音时长太短，继续录音...")
                                    self.last_sound_time = time.time()
                                    self.silence_count = 0
                            else:
                                # 延长检测时间
                                print_with_timestamp(f"检测到短暂静音 ({self.silence_count}/{self.max_silence_count})，延长检测时间...")
                                self.last_sound_time = time.time() + self.speaking_extension
                                
                                if len(self.audio_buffer) > 0:
                                    yield self.audio_buffer, False
                                    self.audio_buffer = np.array([], dtype=np.float32)
                    
                    # 处理完成后短暂休眠，进一步降低CPU使用率
                    await asyncio.sleep(0.01)

                except Exception as e:
                    print_with_timestamp(f"读取音频错误: {e}")
                    self.ensure_device_ready()
                    # 添加短暂休眠
                    await asyncio.sleep(0.1)
                    continue

        finally:
            self.is_recording = False
            print_with_timestamp("录音循环已结束")

    def stop_recording(self):
        """停止录音"""
        print_with_timestamp(f"正在停止录音... (当前状态: is_recording={self.is_recording}, recording_started={self.recording_started})")
        self.is_recording = False
        self.recording_started = False
        self.should_stop = True  # 设置停止标志
        print_with_timestamp("录音标志已重置")
        # 清空缓冲区
        self.audio_buffer = np.array([], dtype=np.float32)
        # 等待一小段时间确保录音完全停止
        time.sleep(0.1)
        print_with_timestamp("录音已停止")


def load_wake_words() -> List[str]:
    try:
        with open('wake_words.txt', 'r', encoding='utf-8') as f:
            # 只加载少量唤醒词以提高效率
            words = [line.strip() for line in f.readlines() if line.strip()]
            # 如果唤醒词太多，只保留前5个
            if len(words) > 5:
                logger.warning(f"唤醒词过多，仅使用前5个: {words[:5]}")
                return words[:5]
            return words
    except FileNotFoundError:
        # 减少默认唤醒词数量
        return ["你好", "哪吒", "在吗"]


async def call_stt_api_async(audio_data):
    url = STT_URL

    try:
        # 确保音频数据是 numpy 数组
        if not isinstance(audio_data, np.ndarray):
            print(f"警告：音频数据类型不正确，当前类型: {type(audio_data)}")
            audio_data = np.array(audio_data)

        # 确保音频数据是一维数组
        if len(audio_data.shape) > 1:
            audio_data = audio_data.flatten()
            
        # 音频数据下采样，减少发送的数据量
        audio_data = audio_data[::2]  # 每隔一个样本取一个，减少一半数据量

        # 简化日志输出，减少CPU和内存消耗
        print(f"发送音频数据，长度={len(audio_data)}")

        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
            async with session.post(url, json={"audio_data": audio_data.tolist()}) as response:
                if response.status == 200:
                    result = await response.json()
                    transcription = result.get("transcription", "")
                    print(f"STT 服务返回结果: {transcription}")
                    return transcription
                else:
                    print(f"STT API 错误: 状态码={response.status}")
                    return ""
    except asyncio.TimeoutError:
        print("STT API 请求超时")
        return ""
    except Exception as e:
        print(f"调用 STT API 错误: {e}")
        return ""

async def wake_word_detection():
    """唤醒词检测"""
    recorder = get_recorder()
    wake_words = load_wake_words()
    current_text = ""
    is_processing = False

    try:
        print_with_timestamp("准备检测唤醒词...")
        while True:
            # 检查是否需要重置
            recorder.check_reset_needed()

            if is_processing:
                await asyncio.sleep(0.2)  # 增加休眠时间
                continue

            recorder.reset()
            is_processing = True

            try:
                async for audio_chunk, timeout in recorder.start_continuous_recording():
                    # 在循环中也检查是否需要重置
                    if recorder.check_reset_needed():
                        print_with_timestamp("执行定时重置，重新开始检测...")
                        break

                    # 检查是否应该停止
                    if recorder.should_stop:
                        print_with_timestamp("检测到停止信号，终止STT处理")
                        break

                    if timeout:
                        print_with_timestamp("检测到静音，重新开始检测唤醒词...")
                        break

                    if len(audio_chunk) == 0:
                        continue

                    # 如果设置了停止标志，不再调用STT服务
                    if recorder.should_stop:
                        break

                    # 降低发送到STT服务的频率
                    if len(audio_chunk) < 8000:  # 如果音频太短，跳过此次处理
                        continue

                    transcription = await call_stt_api_async(audio_chunk)
                    
                    # 再次检查停止标志和重置需求
                    if recorder.should_stop or recorder.check_reset_needed():
                        break

                    if transcription.strip():
                        current_text = transcription
                        print_with_timestamp(f"当前识别: '{current_text}'")

                        for wake_word in wake_words:
                            if wake_word in current_text:
                                print_with_timestamp(f"检测到唤醒词: {wake_word}")
                                # 更新最后重置时间，因为检测到了唤醒词
                                recorder.last_reset_time = time.time()
                                recorder.stop_recording()
                                return {
                                    "status": "success",
                                    "wake_word_detected": True,
                                    "word": wake_word,
                                    "transcription": current_text
                                }

                    # 每次处理后休眠一小段时间，减少CPU使用率
                    await asyncio.sleep(0.1)
            finally:
                is_processing = False
                recorder.stop_recording()

            # 如果设置了停止标志，退出主循环
            if recorder.should_stop:
                break
                
            # 每次检测循环后添加休眠
            await asyncio.sleep(0.2)

    except Exception as e:
        print_with_timestamp(f"唤醒词检测错误: {e}")
        recorder.stop_recording()
        raise HTTPException(status_code=500, detail=str(e))

    return {
        "status": "success",
        "wake_word_detected": False,
        "word": "",
        "transcription": current_text
    }

@app.post("/wake")
async def listen_for_wake_word():
    """唤醒接口"""
    try:
        result = await wake_word_detection()
        if result["wake_word_detected"]:
            # 检测到唤醒词后，停止录音并等待新的请求
            recorder = get_recorder()
            recorder.stop_recording()
        return result
    except Exception as e:
        print(f"处理唤醒请求错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/stop_recording")
async def stop_recording():
    """停止当前录音但不关闭服务"""
    try:
        if global_recorder:
            global_recorder.stop_recording()  # 停止录音
            await asyncio.sleep(0.2)  # 等待录音完全停止
            global_recorder.reset()  # 重置状态
            print_with_timestamp("录音已完全停止并重置")
        return {"status": "success", "message": "录音已停止"}
    except Exception as e:
        print_with_timestamp(f"停止录音失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 添加节能模式设置
@app.post("/set_power_saving")
async def set_power_saving(enabled: bool = True):
    """设置节能模式"""
    try:
        recorder = get_recorder()
        if enabled:
            # 在节能模式下调整参数
            recorder.chunk = 4800  # 更大的块大小
            recorder.sample_rate = 0.3  # 更低的采样率
            recorder.silence_timeout = 1.5  # 更长的静音超时
            print_with_timestamp("节能模式已启用")
        else:
            # 恢复正常模式
            recorder.chunk = 3200
            recorder.sample_rate = 0.4
            recorder.silence_timeout = 1.2
            print_with_timestamp("节能模式已禁用")
        return {"status": "success", "power_saving": enabled}
    except Exception as e:
        print_with_timestamp(f"设置节能模式失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "wake_word_service:app",
        host="0.0.0.0",
        port=8200,
        log_level="info",
        workers=1,  # 减少worker数量
        loop="asyncio"  # 使用asyncio事件循环
    )