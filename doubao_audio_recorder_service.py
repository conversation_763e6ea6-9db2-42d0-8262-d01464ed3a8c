import sys
import requests
import uvicorn
from fastapi import FastAPI, HTTPException
import pyaudio
import numpy as np
import time
import socket
import yaml
from loguru import logger
import asyncio
import websockets
import gzip
import uuid
import datetime
import json

app = FastAPI()

format_string = "[{time:HH:mm:ss.SSS}] {level: <8} | {file}:{line} - {message}"

logger.remove()  # 删除默认处理器
logger.add(sys.stderr, format=format_string)
logger.add(
    "logs/file_{time}.log",
    rotation="50 MB",
    retention="10 days",
    format=format_string,
    enqueue=True  # 使用队列来减少IO阻塞
)


def load_config(config_path="./config/config.yaml"):
    """加载YAML配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
            logger.info(f"成功加载配置文件: {config_path}")
            return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        logger.warning("将使用默认配置")
        return None


# 全局配置
CONFIG = load_config()
# 如果配置加载失败，使用默认值
if CONFIG is None:
    CONFIG = {
        "api": {
            "stt": {
                "ws_url": "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel",
                "uid": "test",
                "format": "pcm",
                "rate": 16000,
                "bits": 16,
                "channel": 1,
                "codec": "raw",
                "streaming": True,
                "seg_duration": 100,
                "auth": {
                    "resource_id": "volc.bigasr.sauc.duration",
                    "access_key": "NqMvkGLbf4dqG-TMgkOj-bGDGITEnPf9",
                    "app_key": "3573626483"
                }
            }
        },
        "audio": {
            "threshold_mode": "fixed",  # 默认使用固定阈值模式
            "fixed_threshold": 10000  # 默认固定阈值
        }
    }
elif "audio" not in CONFIG:
    # 如果配置中没有音频相关设置，添加默认值
    CONFIG["audio"] = {
        "threshold_mode": "fixed",
        "fixed_threshold": 10000
    }

# 确保 STT 配置存在并使用新的格式
if "api" not in CONFIG or "stt" not in CONFIG["api"]:
    CONFIG["api"] = {
        "stt": {
            "ws_url": "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel",
            "uid": "test",
            "format": "pcm",
            "rate": 16000,
            "bits": 16,
            "channel": 1,
            "codec": "raw",
            "streaming": True,
            "seg_duration": 100,
            "auth": {
                "resource_id": "volc.bigasr.sauc.duration",
                "access_key": "NqMvkGLbf4dqG-TMgkOj-bGDGITEnPf9",
                "app_key": "3573626483"
            }
        }
    }
# 如果配置中还存在旧的url字段，删除它
if "url" in CONFIG.get("api", {}).get("stt", {}):
    del CONFIG["api"]["stt"]["url"]

# 音频配置
THRESHOLD_MODE = CONFIG["audio"].get("threshold_mode", "fixed")
FIXED_THRESHOLD = CONFIG["audio"].get("fixed_threshold", 10000)
logger.info(f"THRESHOLD_MODE: {THRESHOLD_MODE}, FIXED_THRESHOLD: {FIXED_THRESHOLD}")


class AudioRecorder:
    def __init__(self):
        self.chunk = 1600
        self.format = pyaudio.paInt16
        self.channels = 1
        self.rate = 16000
        self.threshold = FIXED_THRESHOLD if THRESHOLD_MODE == "fixed" else 5000  # 根据配置设置初始阈值
        self.volume_drop_threshold = 0.3
        self.end_frames_threshold = 30  # 修改为30帧，相当于约3秒的静音
        self.p = pyaudio.PyAudio()  # 在初始化时创建PyAudio实例
        self.stream = None
        self.is_recording = False
        self.noise_threshold = None  # 环境噪音阈值
        self.device_index = self.find_input_device()  # 在初始化时就找到设备

    def find_input_device(self):
        """查找可用的输入设备"""
        # 遍历所有音频设备
        for i in range(self.p.get_device_count()):
            device_info = self.p.get_device_info_by_index(i)
            logger.info(f"设备 {i}: {device_info['name']} - 输入通道: {device_info['maxInputChannels']}")

            # 优先选择USB音频设备
            if ('usb' in device_info['name'].lower() and
                device_info['maxInputChannels'] > 0):
                logger.info(f"选择USB音频设备: {device_info['name']}")
                return i

        # 如果没有找到USB设备，使用默认设备（与ws_wake_service.py保持一致）
        try:
            default_device = self.p.get_default_input_device_info()['index']
            logger.info(f"使用默认输入设备: 索引 {default_device}")
            return default_device
        except Exception as e:
            logger.error(f"无法获取默认输入设备: {e}")
            # 如果都失败了，返回设备5（从日志看这个设备可用）
            logger.info("尝试使用设备索引5")
            return 5

    def start(self):
        """启动录音设备"""
        try:
            if self.stream is None or not self.stream.is_active():
                logger.info(f"正在使用设备索引 {self.device_index} 打开音频流")
                self.stream = self.p.open(
                    format=self.format,
                    channels=self.channels,
                    rate=self.rate,
                    input=True,
                    input_device_index=self.device_index,  # 指定输入设备
                    frames_per_buffer=self.chunk
                )
                logger.info("录音设备已启动")
        except Exception as e:
            logger.error(f"启动录音设备失败: {e}")
            self.stop()
            raise

    def stop(self):
        """停止录音设备"""
        try:
            if self.stream:
                if self.stream.is_active():
                    self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            if self.p:
                self.p.terminate()
                self.p = None
            logger.info("录音设备已停止")
        except Exception as e:
            logger.error(f"停止录音设备失败: {e}")

    def ensure_device_ready(self):
        """确保录音设备就绪"""
        if self.stream is None or not self.stream.is_active():
            self.start()

    def collect_ambient_noise(self, duration=1):
        """收集环境噪音"""
        self.ensure_device_ready()
        logger.info("\n收集环境噪音中...")

        noise_samples = []
        frames_to_collect = int(self.rate * duration / self.chunk)

        for _ in range(frames_to_collect):
            try:
                data = self.stream.read(self.chunk, exception_on_overflow=False)
                audio_data = np.frombuffer(data, dtype=np.int16)
                current_volume = float(np.max(np.abs(audio_data)))
                noise_samples.append(current_volume)
            except Exception as e:
                logger.error(f"收集噪音样本时出错: {e}")
                continue

        if noise_samples:
            # 使用中位数而不是95百分位数
            noise_level = np.median(noise_samples)
            # 使用更大的倍数，并提高最小阈值
            min_threshold = 6000  # 从5000提高到8000
            max_threshold = 20000  # 从15000提高到20000
            calculated_threshold = noise_level * 2.0  # 从1.5提高到2.0

            # 确保阈值在合理范围内
            self.threshold = max(min_threshold, min(max_threshold, calculated_threshold))

            logger.info(f"环境噪音水平: {noise_level:.0f}")
            logger.info(f"设置录音阈值为: {self.threshold:.0f}")

            # 如果环境噪音异常高，给出警告
            if noise_level > max_threshold:
                logger.warning("警告：环境噪音较大，可能会影响录音质量")
        else:
            logger.info("无法收集环境噪音，使用默认阈值")
            self.threshold = 5000

    def record_by_volume(self):
        """根据音量变化判断录音开始和结束"""
        self.ensure_device_ready()

        # 根据配置决定如何设置阈值
        if THRESHOLD_MODE == "auto":
            # 启用自动噪音检测
            self.collect_ambient_noise()
            if self.threshold > 15000:
                logger.info("等待环境噪音降低...")
                time.sleep(1)
                self.collect_ambient_noise()
        elif THRESHOLD_MODE == "fixed":
            # 使用固定阈值
            self.threshold = FIXED_THRESHOLD
            logger.info(f"使用固定录音阈值: {self.threshold}")
        else:  # "default"
            # 使用默认阈值
            self.threshold = 5000
            logger.info(f"使用默认录音阈值: {self.threshold}")

        frames = []
        is_speaking = False
        low_volume_count = 0
        speaking_volumes = []
        initial_wait = 1.5  # 将初始等待时间设置为1.5秒
        extension_time = 0.5  # 延长时间设置为0.5秒
        current_timeout = initial_wait
        start_time = time.time()
        max_duration = 8  # 初始最大录音时长
        last_volume_check = time.time()
        volume_check_interval = 0.2  # 音量检查间隔
        min_volume_threshold = 5000  # 最小音量阈值
        speech_start_time = None  # 记录开始说话的时间
        min_remaining_time = 1.0  # 确保至少有1秒的剩余时间
        silence_duration = 0.0  # 初始化 silence_duration 变量，这个是静默时间

        try:
            logger.info(f"准备录音，初始等待{initial_wait}秒...")

            while True:
                if self.stream is None or not self.stream.is_active():
                    logger.info("录音设备未就绪，重新启动...")
                    self.start()

                data = self.stream.read(self.chunk, exception_on_overflow=False)
                audio_data = np.frombuffer(data, dtype=np.int16)
                current_volume = float(np.max(np.abs(audio_data)))

                elapsed_time = time.time() - start_time
                # 确保remaining_time至少为2秒
                remaining_time = max(min_remaining_time, current_timeout - elapsed_time)

                logger.info(
                    f"当前音量: {current_volume:.0f}, 阈值: {self.threshold:.0f}, 剩余时间: {remaining_time:.1f}秒",
                    end='\r')

                # 检测到说话声音
                if current_volume > self.threshold:
                    if not is_speaking:
                        logger.info(f"\n检测到声音，重置计时... (音量: {current_volume:.0f})")
                        is_speaking = True
                        speech_start_time = time.time()  # 记录开始说话的时间
                    # 重置计时器和起始时间
                    start_time = time.time()
                    # 确保超时时间至少为2秒
                    current_timeout = max(extension_time, min_remaining_time)
                    speaking_volumes.append(current_volume)
                    low_volume_count = 0
                    frames.append(data)

                # 已经在录音状态
                elif is_speaking:
                    frames.append(data)
                    low_volume_count += 1

                    # 定期检查是否还在说话
                    current_time = time.time()
                    if current_time - last_volume_check >= volume_check_interval:
                        last_volume_check = current_time
                        # 计算最近一段时间的平均音量
                        recent_volume = np.mean(
                            [float(np.max(np.abs(np.frombuffer(f, dtype=np.int16)))) for f in frames[-5:]])

                        # 如果音量仍然较高，说明可能还在说话
                        if recent_volume > min_volume_threshold:
                            current_duration = len(frames) * self.chunk / self.rate
                            if current_duration >= max_duration - 1:  # 快到达最大时长
                                max_duration += 2  # 延长2秒
                                logger.info(f"\n检测到正在说话，延长录音时间到 {max_duration} 秒")
                                low_volume_count = 0  # 重置静音计数
                        # 如果检测到当前音量超过阈值的70%，也重置计时
                        elif current_volume > self.threshold * 0.7:
                            start_time = time.time()  # 重置计时器
                            low_volume_count = 0  # 重置低音量计数
                            # 确保超时时间至少为2秒
                            current_timeout = max(extension_time, min_remaining_time)

                    # 计算当前帧数对应的秒数
                    silence_duration = low_volume_count * self.chunk / self.rate
                    if silence_duration >= 1.0:  # 静音持续2秒后结束录音
                        logger.info(f"\n检测到说话结束，静音持续了 {silence_duration:.1f} 秒")
                        break

                # 检查是否达到当前超时时间 - 只有在真正超过超时时间且至少等待2秒时才结束
                if elapsed_time >= current_timeout and elapsed_time >= min_remaining_time:
                    if not is_speaking:
                        logger.info(f"\n已等待 {elapsed_time:.1f} 秒，未检测到声音，录音结束")
                        return None
                    elif silence_duration >= 1.0:  # 降低超时检查的静音时间要求为1秒
                        logger.info(f"\n达到超时时间且检测到静音 {silence_duration:.1f} 秒，录音结束")
                        break
                    else:
                        # 如果还没到静音时间但超时了，延长一些时间
                        start_time = time.time()
                        # 确保延长的超时时间至少为2秒
                        current_timeout = max(2.0, extension_time)
                        logger.info(f"\n延长录音时间，新的剩余时间: {current_timeout:.1f}秒")

                # 检查是否达到最大录音时长
                if len(frames) * self.chunk / self.rate > max_duration:
                    logger.info(f"\n达到最大录音时长 {max_duration} 秒，结束录音")
                    break

            if not frames or len(frames) < 8:  # 保至少有一定长度的录音
                logger.info("\n未检测到有效语音")
                return None

            # 处理音频数据
            wav_data = np.frombuffer(b''.join(frames), dtype=np.int16).astype(np.float32)
            wav_data /= 32768.0

            return wav_data

        except Exception as e:
            logger.error(f"\n录音错误: {e}")
            return None
        finally:
            self.is_recording = False


# 修改 AsrConfig 类使用配置文件的值
class AsrConfig:
    def __init__(self, config):
        stt_config = config["api"]["stt"]
        self.ws_url = stt_config["ws_url"]
        self.uid = stt_config["uid"]
        self.format = stt_config["format"]
        self.rate = stt_config["rate"]
        self.bits = stt_config["bits"]
        self.channel = stt_config["channel"]
        self.codec = stt_config["codec"]
        self.streaming = stt_config["streaming"]
        self.seg_duration = stt_config["seg_duration"]
        self.auth = stt_config["auth"]


# 创建全局配置实例
ASR_CONFIG = AsrConfig(CONFIG)


# 修改 call_stt_api 函数使用新的配置
async def call_stt_api(audio_data):
    """使用 WebSocket 调用语音识别服务"""
    try:
        # 创建临时音频数据
        audio_bytes = (audio_data * 32768.0).astype(np.int16).tobytes()

        # 创建请求对象，使用配置文件的值
        asr_client = AsrWsClient(
            is_mic=False,
            format=ASR_CONFIG.format,
            rate=ASR_CONFIG.rate,
            bits=ASR_CONFIG.bits,
            channel=ASR_CONFIG.channel,
            streaming=ASR_CONFIG.streaming,
            seg_duration=ASR_CONFIG.seg_duration,
            ws_url=ASR_CONFIG.ws_url,
            uid=ASR_CONFIG.uid,
            auth=ASR_CONFIG.auth
        )

        # 执行识别
        result = await asr_client.segment_data_processor(audio_bytes, len(audio_bytes))

        # 提取识别结果
        if result and 'payload_msg' in result and 'result' in result['payload_msg']:
            transcription = result['payload_msg']['result'].get('text', '')
            return transcription

        return ""

    except Exception as e:
        logger.error(f"语音识别错误: {e}")
        return ""


# 修改 listen_and_save 函数
def listen_and_save():
    """录音并返回处理后的数据"""
    try:
        recorder = AudioRecorder()
        recorder.start()  # 确保设备启动
        audio_data = recorder.record_by_volume()

        if audio_data is None:
            return {
                "status": "timeout",
                "message": "未检测到语音",
                "audio_data": [],
                "transcription": ""
            }

        logger.info("开始转录...")
        # 使用 asyncio 运行异步 STT 调用
        transcription = asyncio.run(call_stt_api(audio_data))
        logger.info(f"转录完成: {transcription}")

        return {
            "status": "success",
            "message": "录音完成",
            "audio_data": audio_data.tolist(),
            "transcription": transcription
        }
    except Exception as e:
        logger.error(f"录音或转录错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/record")
def record_audio():
    """录音接口"""
    try:
        return listen_and_save()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/prepare")
def prepare_service():
    """准备录音设备"""
    recorder = AudioRecorder()
    return {"status": "ready"}


def is_port_in_use(port: int) -> bool:
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('0.0.0.0', port))
            return False
        except OSError:
            return True


def find_available_port(start_port: int, max_attempts: int = 10) -> int:
    port = start_port
    while port < start_port + max_attempts:
        if not is_port_in_use(port):
            return port
        port += 1
    raise RuntimeError(f"No available ports found between {start_port} and {start_port + max_attempts - 1}")


if __name__ == "__main__":
    try:
        port = 8400
        if is_port_in_use(port):
            logger.info(f"端口 {port} 已被占用，尝试关闭之前的进程...")
            import os

            os.system(f'taskkill /F /PID {os.getpid()}')
            time.sleep(1)

            if is_port_in_use(port):
                port = find_available_port(8301)
                logger.info(f"使用新端口: {port}")

        # 确保配置中使用的是新的WebSocket配置
        logger.info(f"使用WebSocket URL: {CONFIG['api']['stt']['ws_url']}")

        uvicorn.run(
            "doubao_audio_recorder_service:app",
            host="0.0.0.0",
            port=port,
            log_level="info"
        )
    except Exception as e:
        logger.error(f"启动服务失败: {e}")
        import traceback

        logger.error(traceback.format_exc())  # 打印完整的错误堆栈

# 添加必要的常量定义
PROTOCOL_VERSION = 0b0001
DEFAULT_HEADER_SIZE = 0b0001

# Message Type:
FULL_CLIENT_REQUEST = 0b0001
AUDIO_ONLY_REQUEST = 0b0010
FULL_SERVER_RESPONSE = 0b1001
SERVER_ACK = 0b1011
SERVER_ERROR_RESPONSE = 0b1111

# Message Type Specific Flags
NO_SEQUENCE = 0b0000
POS_SEQUENCE = 0b0001
NEG_SEQUENCE = 0b0010
NEG_WITH_SEQUENCE = 0b0011
NEG_SEQUENCE_1 = 0b0011

# Message Serialization
NO_SERIALIZATION = 0b0000
JSON = 0b0001

# Message Compression
NO_COMPRESSION = 0b0000
GZIP = 0b0001


# 添加必要的辅助函数
def generate_header(
        message_type=FULL_CLIENT_REQUEST,
        message_type_specific_flags=NO_SEQUENCE,
        serial_method=JSON,
        compression_type=GZIP,
        reserved_data=0x00
):
    """生成WebSocket请求头"""
    header = bytearray()
    header_size = 1
    header.append((PROTOCOL_VERSION << 4) | header_size)
    header.append((message_type << 4) | message_type_specific_flags)
    header.append((serial_method << 4) | compression_type)
    header.append(reserved_data)
    return header


def generate_before_payload(sequence: int):
    """生成payload前的序列号"""
    before_payload = bytearray()
    before_payload.extend(sequence.to_bytes(4, 'big', signed=True))
    return before_payload


def parse_response(res):
    """解析WebSocket响应"""
    protocol_version = res[0] >> 4
    header_size = res[0] & 0x0f
    message_type = res[1] >> 4
    message_type_specific_flags = res[1] & 0x0f
    serialization_method = res[2] >> 4
    message_compression = res[2] & 0x0f
    reserved = res[3]
    header_extensions = res[4:header_size * 4]
    payload = res[header_size * 4:]
    result = {
        'is_last_package': False,
    }
    payload_msg = None
    payload_size = 0
    if message_type_specific_flags & 0x01:
        seq = int.from_bytes(payload[:4], "big", signed=True)
        result['payload_sequence'] = seq
        payload = payload[4:]

    if message_type_specific_flags & 0x02:
        result['is_last_package'] = True

    if message_type == FULL_SERVER_RESPONSE:
        payload_size = int.from_bytes(payload[:4], "big", signed=True)
        payload_msg = payload[4:]
    elif message_type == SERVER_ACK:
        seq = int.from_bytes(payload[:4], "big", signed=True)
        result['seq'] = seq
        if len(payload) >= 8:
            payload_size = int.from_bytes(payload[4:8], "big", signed=False)
            payload_msg = payload[8:]
    elif message_type == SERVER_ERROR_RESPONSE:
        code = int.from_bytes(payload[:4], "big", signed=False)
        result['code'] = code
        payload_size = int.from_bytes(payload[4:8], "big", signed=False)
        payload_msg = payload[8:]
    if payload_msg is None:
        return result
    if message_compression == GZIP:
        payload_msg = gzip.decompress(payload_msg)
    if serialization_method == JSON:
        payload_msg = json.loads(str(payload_msg, "utf-8"))
    elif serialization_method != NO_SERIALIZATION:
        payload_msg = str(payload_msg, "utf-8")
    result['payload_msg'] = payload_msg
    result['payload_size'] = payload_size
    return result


# 添加 AsrWsClient 类
class AsrWsClient:
    def __init__(self, **kwargs):
        self.seg_duration = int(kwargs.get("seg_duration", 100))
        self.ws_url = kwargs.get("ws_url", "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel")
        self.uid = kwargs.get("uid", "test")
        self.format = kwargs.get("format", "wav")
        self.rate = kwargs.get("rate", 16000)
        self.bits = kwargs.get("bits", 16)
        self.channel = kwargs.get("channel", 1)
        self.codec = kwargs.get("codec", "raw")
        self.streaming = kwargs.get("streaming", True)
        self.is_mic = kwargs.get("is_mic", False)
        self.auth = kwargs.get("auth", {
            "resource_id": "volc.bigasr.sauc.duration",
            "access_key": "NqMvkGLbf4dqG-TMgkOj-bGDGITEnPf9",
            "app_key": "3573626483"
        })

    def construct_request(self, reqid):
        """构造请求参数"""
        req = {
            "user": {
                "uid": self.uid,
            },
            "audio": {
                'format': self.format,
                "sample_rate": self.rate,
                "bits": self.bits,
                "channel": self.channel,
                "codec": self.codec,
            },
            "request": {
                "model_name": "bigmodel",
                "enable_punc": True,
            }
        }
        return req

    async def segment_data_processor(self, wav_data: bytes, segment_size: int):
        """处理音频数据"""
        reqid = str(uuid.uuid4())
        seq = 1
        request_params = self.construct_request(reqid)
        payload_bytes = str.encode(json.dumps(request_params))
        payload_bytes = gzip.compress(payload_bytes)

        header = {
            "X-Api-Resource-Id": self.auth["resource_id"],
            "X-Api-Access-Key": self.auth["access_key"],
            "X-Api-App-Key": self.auth["app_key"],
            "X-Api-Request-Id": reqid
        }

        try:
            async with websockets.connect(self.ws_url, extra_headers=header, max_size=1000000000) as ws:
                # 发送初始请求
                full_client_request = bytearray(generate_header(message_type_specific_flags=POS_SEQUENCE))
                full_client_request.extend(generate_before_payload(sequence=seq))
                full_client_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
                full_client_request.extend(payload_bytes)

                await ws.send(full_client_request)
                res = await ws.recv()
                result = parse_response(res)

                # 处理音频数据
                for chunk, last in self.slice_data(wav_data, segment_size):
                    seq += 1
                    if last:
                        seq = -seq

                    payload_bytes = gzip.compress(chunk)
                    audio_only_request = bytearray(generate_header(
                        message_type=AUDIO_ONLY_REQUEST,
                        message_type_specific_flags=NEG_WITH_SEQUENCE if last else POS_SEQUENCE
                    ))
                    audio_only_request.extend(generate_before_payload(sequence=seq))
                    audio_only_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
                    audio_only_request.extend(payload_bytes)

                    await ws.send(audio_only_request)
                    res = await ws.recv()
                    result = parse_response(res)

                    if self.streaming:
                        await asyncio.sleep(self.seg_duration / 1000.0)

                return result

        except Exception as e:
            logger.error(f"WebSocket通信错误: {e}")
            return None

    @staticmethod
    def slice_data(data: bytes, chunk_size: int):
        """将数据切片"""
        data_len = len(data)
        offset = 0
        while offset + chunk_size < data_len:
            yield data[offset: offset + chunk_size], False
            offset += chunk_size
        else:
            yield data[offset: data_len], True