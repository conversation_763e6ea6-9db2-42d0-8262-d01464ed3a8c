---
description: 
globs: 
alwaysApply: false
---
# 开发和部署指南

## 开发环境
- 开发平台：Windows
- 目标部署平台：树莓派 4B
- Python 版本：3.11

## 性能优化注意事项

### 跨平台兼容性
- 文件路径使用 `os.path` 处理，避免硬编码路径分隔符
- 系统调用需考虑平台差异，使用 `platform` 模块判断
- 音频播放与恐龙动作相关代码参考 [audio_action_controller.py](mdc:audio_action_controller.py)

### 资源管理
- 音频处理时注意内存使用，及时释放资源
- 使用 `with` 语句管理文件和资源
- 大文件处理采用流式处理
- 参考音频录制服务 [audio_recorder_service.py](mdc:audio_recorder_service.py)

### 性能优化
- CPU密集型任务考虑使用多进程 `multiprocessing`
- IO密集型任务使用异步处理 `asyncio`
- 避免频繁的文件IO操作
- 参考语音控制服务 [voice_control_service.py](mdc:voice_control_service.py)

### 树莓派特定优化
- 注意CPU温度监控
- 避免频繁写入SD卡
- 考虑使用内存文件系统处理临时文件
- 日志文件定期清理

## 依赖管理
- 所有依赖版本在 [requirements.txt](mdc:requirements.txt) 中明确指定
- 避免使用平台特定的依赖
- 优先使用轻量级库

## 调试和日志
- 使用 `loguru` 进行日志记录
- 开发时启用详细日志，部署时调整日志级别
- 日志文件存放在 `logs` 目录

