# -*- coding: utf-8 -*-
import os
import sys
import time
import asyncio
import argparse
import platform
import aiohttp
import importlib
from logger_config import setup_logger

# 设置日志
logger = setup_logger(module_name="main")

# 导入自定义模块
try:
    from config_loader import get_config
    from new_code.state_manager import (
        initialize as init_state_manager, 
        get_state_info, 
        can_continue_conversation,
        can_enter_mq_state,
        set_mq_state,
        is_in_mq_state
    )
    from new_code.idle_audio_manager import initialize as init_idle_audio_manager, stop as stop_idle_audio
    from new_code.conversation_system import (
        check_for_wakeword, process_conversation, check_timeout, initialize as init_conversation
    )
    from new_code.ai_service import (
        check_availability as check_ai_availability,
        text_response, extract_text_and_actions
    )
    from new_code.mqtt_client import AsyncMQTTClient

except ImportError:
    # 如果是直接运行这个文件，需要调整导入路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from new_code.config_loader import get_config
    from new_code.state_manager import (
        initialize as init_state_manager, 
        get_state_info, 
        can_continue_conversation,
        can_enter_mq_state,
        set_mq_state,
        is_in_mq_state
    )
    from new_code.idle_audio_manager import initialize as init_idle_audio_manager, stop as stop_idle_audio
    from new_code.conversation_system import (
        check_for_wakeword, process_conversation, check_timeout, initialize as init_conversation
    )
    from new_code.ai_service import (
        check_availability as check_ai_availability,
        text_response, extract_text_and_actions
    )
    from new_code.mqtt_client import AsyncMQTTClient

# 全局配置变量
CONFIG = None

# 全局MQTT客户端
MQTT_CLIENT = None

def get_tts_service(tts_type):
    """获取TTS服务模块"""
    tts_services = {
        "doubao": "new_code.doubao_tts_service",
        "hailuo": "new_code.hailuo_tts_service",
        "default": "new_code.tts_service",
        # 在这里添加新的TTS服务
        "new_tts": "new_code.new_tts_service"
    }
    
    if tts_type not in tts_services:
        logger.warning(f"未知的TTS类型: {tts_type}，使用默认TTS服务")
        tts_type = "default"
    
    try:
        service_module = importlib.import_module(tts_services[tts_type])
        return service_module
    except ImportError as e:
        logger.error(f"加载TTS服务失败: {e}")
        return None

async def handle_mqtt_message(topic, data):
    """处理MQTT消息"""
    try:
        # 检查当前状态
        state_info = get_state_info()
        
        # 如果正在对话中，忽略MQTT消息
        if state_info["in_conversation"]:
            logger.info("当前正在对话中，忽略MQTT消息")
            return
            
        # 如果正在播放闲置音频，也忽略MQTT消息
        if state_info["is_playing_idle"]:
            logger.info("当前正在播放闲置音频，忽略MQTT消息")
            return
        
        # 获取要合成的文本
        text = None
        if topic == "KLWOWN":
            # KLWOWN主题的消息已经在mqtt_client中处理过
            text = data.get("text")  # 直接获取处理后的文本
            logger.info(f"收到KLWOWN消息:")
            logger.info(f"  ID: {data.get('id')}")
            logger.info(f"  时间: {data.get('time')}")
            logger.info(f"  类型: {data.get('type')}")
            logger.info(f"  文本内容: {text}")
        else:
            # 其他主题的消息处理
            if isinstance(data, dict):
                text = data.get("text")
            elif isinstance(data, str):
                text = data
        
        if text:
            logger.info(f"准备合成语音: {text}")
            
            # 设置MQ状态
            if not is_in_mq_state():
                logger.info("收到MQTT消息，进入MQ状态")
                set_mq_state(True)
            
            # 获取TTS服务
            tts_type = CONFIG["api"]["tts"].get("type", "default")
            tts_service = get_tts_service(tts_type)
            
            if tts_service:
                # 合成并播放语音
                await asyncio.to_thread(tts_service.synthesize_speech, text)
                
                # 处理完消息后延迟退出MQ状态
                asyncio.create_task(delayed_exit_mq_state())
            else:
                logger.error("无法加载TTS服务")
                set_mq_state(False)
    except Exception as e:
        logger.error(f"处理MQTT消息时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        set_mq_state(False)

async def delayed_exit_mq_state(delay=2):
    """延迟退出MQ状态"""
    await asyncio.sleep(delay)
    if is_in_mq_state():
        logger.info("延迟退出MQ状态")
        set_mq_state(False)

async def init_mqtt_client():
    """初始化MQTT客户端"""
    global MQTT_CLIENT, CONFIG
    
    if not CONFIG["mqtt"]["enabled"]:
        logger.info("MQTT功能未启用")
        return
        
    try:
        # 创建MQTT客户端
        MQTT_CLIENT = AsyncMQTTClient(
            broker_host=CONFIG["mqtt"]["broker"]["host"],
            broker_port=CONFIG["mqtt"]["broker"]["port"],
            username=CONFIG["mqtt"]["broker"]["username"],
            password=CONFIG["mqtt"]["broker"]["password"],
            device_id=CONFIG["mqtt"]["device_id"]
        )
        
        # 设置消息处理回调
        MQTT_CLIENT.set_message_callback(handle_mqtt_message)
        
        # 连接到服务器
        connected = await MQTT_CLIENT.connect()
        if connected:
            # 订阅配置的主题
            for topic in CONFIG["mqtt"]["topics"]:
                MQTT_CLIENT.subscribe(topic["name"], topic["qos"])
        else:
            logger.error("MQTT客户端连接失败")
            
    except Exception as e:
        logger.error(f"初始化MQTT客户端时出错: {e}")

async def start_system():
    """启动系统"""
    global CONFIG
    
    # 初始化MQTT客户端
    await init_mqtt_client()
    
    # 检查AI服务是否可用
    api_available = await check_ai_availability()
    if not api_available:
        logger.warning("豆包AI服务不可用，请检查网络连接和API配置")
        logger.warning("尝试继续运行，但可能会影响系统功能")
    
    # 初始化闲置音频管理器
    if CONFIG["idle_audio"]["enabled"]:
        logger.info("初始化闲置音频播放功能")
        init_idle_audio_manager()
    else:
        logger.info("闲置音频播放功能已禁用")
    
    # 播放启动提示音
    try:
        logger.info("合成启动提示音")
        tts_type = CONFIG["api"]["tts"].get("type", "default")
        tts_service = get_tts_service(tts_type)
        
        if tts_service:
            tts_service.synthesize_speech("服务已连接，请等待3秒后开始对话")
        else:
            logger.error("无法加载TTS服务")
    except Exception as e:
        logger.error(f"合成开机提示音失败: {e}")
    
    # 检查连续对话功能配置
    continuous_dialog_enabled = CONFIG.get("conversation", {}).get("continuous_dialog", True)
    if continuous_dialog_enabled:
        logger.info("连续对话功能已启用，对话后将自动等待下一次输入")
    else:
        logger.info("连续对话功能未启用，每次对话后需要重新唤醒")
    
    # 主循环
    logger.info("开始主循环...")
    while True:
        try:
            # 检查对话超时
            check_timeout()
            
            # 获取当前状态
            state_info = get_state_info()
            
            # 检查是否启用了连续对话功能
            continuous_dialog_enabled = CONFIG.get("conversation", {}).get("continuous_dialog", True)
            
            # 在连续对话模式下，如果可以继续对话，则直接开始录音等待用户输入
            if continuous_dialog_enabled and can_continue_conversation():
                logger.info("连续对话模式已激活，主动等待用户输入")
                await process_conversation()
            # 只有在非对话状态时才检查唤醒词
            elif not state_info["in_conversation"] and await check_for_wakeword():
                logger.info("检测到唤醒词，开始处理对话")
                await process_conversation()
            
            # 短暂等待，避免CPU占用过高
            await asyncio.sleep(0.1)
            
        except asyncio.CancelledError:
            logger.info("主循环被取消，准备退出")
            break
        except Exception as e:
            logger.error(f"主循环发生异常: {e}")
            import traceback
            logger.error(traceback.format_exc())
            await asyncio.sleep(1)  # 出错后稍等一会再继续

async def stop_system():
    """停止系统"""
    global CONFIG, MQTT_CLIENT
    
    logger.info("正在停止系统...")
    
    # 断开MQTT连接
    if MQTT_CLIENT:
        MQTT_CLIENT.disconnect()
        logger.info("MQTT客户端已断开连接")
    
    # 停止闲置音频管理器
    if CONFIG["idle_audio"]["enabled"]:
        stop_idle_audio()
        logger.info("闲置音频管理器已停止")
    
    # 直接使用默认的停止方法
    await _stop_default_recording()
    
    logger.info("系统已停止")

async def _stop_default_recording():
    """停止默认录音服务"""
    global CONFIG
    
    try:
        wake_word_url = CONFIG["services"]["wake_word"]["url"]
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{wake_word_url}/stop_recording") as response:
                if response.status == 200:
                    logger.info("收到停止录音请求，停止录音")
                else:
                    logger.error("停止录音失败")
    except Exception as e:
        logger.error(f"停止录音失败: {e}")
    
async def test_mode(query=None):
    """测试模式"""
    logger.info("=== 测试模式启动 ===")
    
    # 检查AI服务是否可用
    api_available = await check_ai_availability()
    if not api_available:
        logger.error("豆包AI服务连接测试失败，请检查API密钥和网络")
        return
    
    # 获取测试文本
    if query is None:
        query = input("请输入测试的文本: ")
    logger.info(f"使用测试文本: {query}")
    
    # 调用AI服务
    start_time = time.time()
    response, messages = await text_response(query, [])
    logger.info(f"AI响应时间: {time.time() - start_time:.2f}秒")
    
    if response:
        logger.info(f"豆包API回复: {response}")
        
        # 解析文本和动作
        clean_text, actions = extract_text_and_actions(response)
        logger.info(f"纯文本: {clean_text}")
        logger.info(f"动作标记: {actions}")
        
        # 测试TTS服务
        logger.info("测试语音合成...")
        
        try:
            # 判断使用哪个tts，然后导入包
            if CONFIG["tts"]["type"] == "doubao":
                from new_code.doubao_tts_service import synthesize_speech
            else:
                from new_code.tts_service import synthesize_speech
            synthesize_speech(clean_text)
            logger.info("语音合成和播放测试完成")
        except Exception as e:
            logger.error(f"语音合成测试失败: {e}")
    else:
        logger.info("豆包API无响应")
    
    logger.info("=== 测试完成 ===")

def main():
    """主函数"""
    global CONFIG
    
    # 命令行参数解析
    parser = argparse.ArgumentParser(description='恐龙对话系统')
    parser.add_argument('--test', action='store_true', help='测试模式，无需唤醒服务')
    parser.add_argument('--query', type=str, help='测试模式下的测试文本')
    parser.add_argument('--config', type=str, default="./config/config.yaml", help='配置文件路径')
    args = parser.parse_args()
    
    # 加载配置文件
    CONFIG = get_config(args.config)
    
    # 如果指定了配置文件，重新加载
    if args.config != "./config/config.yaml":
        CONFIG = get_config(args.config)
        if CONFIG is None:
            logger.error(f"无法加载指定的配置文件: {args.config}，使用默认配置")
    
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)
    
    # 初始化状态管理器
    init_state_manager()
    
    # 初始化对话系统
    init_conversation()
    
    # 初始化事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        if args.test:
            # 测试模式
            loop.run_until_complete(test_mode(args.query))
        else:
            # 正常模式
            async def shutdown(loop):
                """优雅关闭程序"""
                logger.info("正在关闭程序...")
                await stop_system()
                
                # 关闭所有待处理的任务
                tasks = [t for t in asyncio.all_tasks(loop) if t is not asyncio.current_task(loop)]
                for task in tasks:
                    task.cancel()
                
                await asyncio.gather(*tasks, return_exceptions=True)
                loop.stop()
            
            try:
                # 注册信号处理
                if platform.system() != "Windows":
                    import signal
                    
                    for sig in (signal.SIGINT, signal.SIGTERM):
                        loop.add_signal_handler(
                            sig, lambda s=sig: asyncio.create_task(shutdown(loop))
                        )
                
                # 启动系统
                loop.run_until_complete(start_system())
            except KeyboardInterrupt:
                loop.run_until_complete(shutdown(loop))
    except Exception as e:
        logger.error(f"程序发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        loop.close()
        logger.info("程序已退出")

if __name__ == "__main__":
    main() 