# 恐龙对话系统（函数式版本）

这是恐龙对话系统的函数式重构版本。项目从之前的面向对象的类方式重构为使用模块级别的函数，代码更简洁，依赖关系更清晰。

## 项目结构

- `main.py`: 主程序入口，包含程序启动、停止和测试功能
- `config_loader.py`: 配置加载模块，负责读取和管理配置信息
- `state_manager.py`: 状态管理模块，管理系统各个组件的状态
- `ai_service.py`: AI服务模块，负责与豆包API交互获取对话内容
- `tts_service.py`: 语音合成服务，将文本转换为语音
- `idle_audio_manager.py`: 闲置音频管理模块，在系统空闲时播放背景音频
- `conversation_system.py`: 对话系统模块，处理唤醒、录音、识别和对话流程

## 功能特点

- 支持语音唤醒，接受用户语音输入
- 使用AI API处理用户问题并返回回答
- 支持语音合成，以语音形式回答用户问题
- 能够检测对话超时，自动结束会话
- 提供闲置时的背景音频播放
- 支持测试模式，可绕过唤醒和录音步骤直接测试AI和语音合成功能

## 使用方法

1. 标准模式运行：
   ```
   python main.py
   ```

2. 使用指定配置文件：
   ```
   python main.py --config path/to/config.yaml
   ```

3. 测试模式：
   ```
   python main.py --test
   ```

4. 测试模式并指定测试文本：
   ```
   python main.py --test --query "你好，请问今天天气如何？"
   ```

## 配置说明

系统使用YAML配置文件，默认路径为`../config/config.yaml`。主要配置包括：

- API相关配置（豆包AI、语音合成）
- 音频播放配置
- 闲置音频设置
- 对话超时设置
- 唤醒词服务设置

## 重构说明

本项目是从面向对象版本重构为函数式编程风格，主要变化：

1. 移除了类定义，改用模块级函数
2. 使用模块级全局变量替代类成员变量
3. 函数之间通过显式参数传递而不是共享对象状态
4. 使用模块初始化函数替代类构造函数
5. 状态管理更加集中和显式

## 依赖关系

- Python 3.7+
- aiohttp: 异步HTTP客户端/服务器
- loguru: 日志处理
- numpy/scipy/pygame: 音频处理
- 需要外部配置的唤醒词服务和录音服务 