# -*- coding: utf-8 -*-
import os
import sys
import random
import time
import datetime
from threading import Timer
from logger_config import setup_logger

# 设置日志
logger = setup_logger(module_name="hailuo_tts_service")

# 导入自定义模块
try:
    from new_code.config_loader import get_config
    from new_code.state_manager import can_play_idle_audio, set_playing_idle, set_processing
except ImportError:
    # 如果是直接运行这个文件，需要调整导入路径
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from new_code.config_loader import get_config
    from new_code.state_manager import can_play_idle_audio, set_playing_idle, set_processing

# 导入音频动作控制模块
# 暂时使用原始模块，后续可能会重构
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from audio_action_controller import play_idle_audio_with_action


# 全局变量
_config = None
_timer = None
_current_audio_index = 0  # 新增：记录当前播放的音频索引

# 时间段默认定义 (将在initialize时用配置文件中的值替换)
TIME_PERIODS = {
    "morning": {"start": (6, 0), "end": (9, 0)},  # 早晨 6:00-9:00
    "break_time": {"start": (9, 0), "end": (15, 30)},  # 课间 9:00-15:30
    "afternoon": {"start": (15, 30), "end": (18, 0)}  # 下午 15:30-18:00
}

def initialize():
    """初始化闲置音频管理器"""
    global _config, _current_audio_index
    _config = get_config()
    _current_audio_index = 0  # 重置音频索引
    
    # 确保音频目录存在
    os.makedirs(_config["idle_audio"]["directory"], exist_ok=True)
    logger.info(f"确保闲置音频目录存在: {_config['idle_audio']['directory']}")
    
    # 如果启用了基于时间的播放，确保各时段的音频目录存在
    if _config["idle_audio"].get("time_based_playback", False):
        # 加载配置文件中的时间段定义
        if "time_periods" in _config["idle_audio"]:
            # 清空默认配置，使用配置文件中的定义
            TIME_PERIODS.clear()
            for period_name, period_config in _config["idle_audio"]["time_periods"].items():
                # 更新TIME_PERIODS全局变量
                TIME_PERIODS[period_name] = {
                    "start": (period_config["start_hour"], period_config["start_minute"]),
                    "end": (period_config["end_hour"], period_config["end_minute"])
                }
                logger.info(f"加载时间段配置: {period_name} = {TIME_PERIODS[period_name]}")

                # 确保时间段对应的目录存在
                period_dir = os.path.join(_config["idle_audio"]["directory"], period_name)
                os.makedirs(period_dir, exist_ok=True)
                logger.info(f"确保{period_name}音频目录存在: {period_dir}")
        else:
            logger.warning("配置文件中未找到time_periods配置，使用默认时间段设置")
            # 确保默认时间段目录存在
            for period_name in TIME_PERIODS.keys():
                period_dir = os.path.join(_config["idle_audio"]["directory"], period_name)
                os.makedirs(period_dir, exist_ok=True)
                logger.info(f"确保{period_name}音频目录存在: {period_dir}")
    
    # 计划第一次播放
    schedule_next_play()
    logger.info("闲置音频管理器初始化完成，已安排首次播放")

def get_current_time_period():
    """获取当前时间所属的时段"""
    global TIME_PERIODS

    now = datetime.datetime.now()
    current_hour = now.hour
    current_minute = now.minute

    # 转换为小数形式方便比较
    current_time_decimal = current_hour + current_minute / 60

    # 检查各个时间段
    for period_name, period_config in TIME_PERIODS.items():
        start_hour, start_minute = period_config["start"]
        end_hour, end_minute = period_config["end"]

        start_time_decimal = start_hour + start_minute / 60
        end_time_decimal = end_hour + end_minute / 60

        if start_time_decimal <= current_time_decimal < end_time_decimal:
            return period_name

    # 默认返回break_time
    return "break_time"

def play_random_audio():
    """播放随机闲置音频"""
    global _config, _current_audio_index
    
    if not _config:
        initialize()
    
    logger.info("尝试播放闲置音频...")
    
    # 判断当前是否能播放闲置音频
    if not can_play_idle_audio():
        logger.info("当前状态不允许播放闲置音频，跳过本次播放并重新调度")
        schedule_next_play()
        return
    
    # 获取所有可播放的音频文件
    audio_files = _get_audio_files()
    if not audio_files:
        logger.warning(f"未找到可播放的闲置音频文件，目录: {_config['idle_audio']['directory']}")
        schedule_next_play()
        return
    
    # 获取当前要播放的文件
    audio_file = audio_files[_current_audio_index]
    
    # 更新索引，实现循环
    _current_audio_index = (_current_audio_index + 1) % len(audio_files)

    logger.info(f"准备播放闲置音频: {audio_file}")
    
    try:
        # 设置状态为播放闲置音频
        logger.info("设置状态: is_playing_idle = True, is_processing = True")
        set_playing_idle(True)
        set_processing(True)
        
        # 播放音频
        logger.info(f"开始播放闲置音频: {audio_file}")
        before_play_time = time.time()
        
        # 调用音频动作控制器播放音频
        success = play_idle_audio_with_action(audio_file)
        
        play_duration = time.time() - before_play_time
        
        if not success:
            logger.error(f"播放闲置音频失败: {audio_file}, 耗时: {play_duration:.2f}秒")
        else:
            logger.info(f"闲置音频播放完成: {audio_file}, 耗时: {play_duration:.2f}秒")
            
    except Exception as e:
        logger.error(f"播放闲置音频时发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        
    finally:
        # 重置状态
        logger.info("重置状态: is_playing_idle = False, is_processing = False")
        set_playing_idle(False)
        set_processing(False)
        
        # 调度下一次播放
        logger.info("调度下一次闲置音频播放")
        schedule_next_play()

def schedule_next_play():
    """调度下一次闲置音频播放"""
    global _timer, _config
    
    if not _config:
        initialize()
    
    # 如果闲置音频播放功能已禁用，直接返回
    if not _config["idle_audio"]["enabled"]:
        logger.info("闲置音频播放功能已禁用，不再调度")
        return
    
    # 取消已有的计时器（如果有）
    if _timer is not None:
        _timer.cancel()
        logger.info("已取消现有的闲置音频播放计时器")
    
    # 随机生成下次播放的间隔时间
    min_interval = _config["idle_audio"]["min_interval"]
    max_interval = _config["idle_audio"]["max_interval"]
    # print(f"min_interval: {min_interval}, max_interval: {max_interval}")
    next_interval = random.randint(min_interval, max_interval)
    
    logger.info(f"计划在 {next_interval:.2f} 秒后播放下一个闲置音频")
    
    # 创建新的计时器
    _timer = Timer(next_interval, play_random_audio)
    _timer.daemon = True
    _timer.start()

def stop():
    """停止闲置音频管理器"""
    global _timer
    if _timer is not None:
        _timer.cancel()
        _timer = None
        logger.info("闲置音频管理器已停止")

def _find_audio_files_case_insensitive(directory):
    """在指定目录中查找音频文件，处理大小写敏感性问题（适配树莓派）"""
    audio_files = []

    if not os.path.exists(directory):
        logger.warning(f"目录不存在: {directory}")
        return audio_files

    try:
        # 获取目录中的所有文件
        all_files = os.listdir(directory)

        # 支持的音频扩展名（小写）
        audio_extensions = ['.wav', '.mp3']

        for filename in all_files:
            # 检查文件扩展名（不区分大小写）
            file_lower = filename.lower()
            if any(file_lower.endswith(ext) for ext in audio_extensions):
                full_path = os.path.join(directory, filename)
                if os.path.isfile(full_path):
                    audio_files.append(full_path)

        logger.info(f"在目录 {directory} 中找到 {len(audio_files)} 个音频文件")

    except Exception as e:
        logger.error(f"搜索音频文件时发生错误: {e}")

    return audio_files

def _get_audio_files():
    """根据当前时段获取可播放的音频文件"""
    global _config

    if not _config:
        initialize()

    # 获取基础音频目录
    base_audio_dir = _config["idle_audio"]["directory"]
    audio_files = []

    # 如果启用了基于时间的播放
    if _config["idle_audio"].get("time_based_playback", False):
        # 获取当前时段
        time_period = get_current_time_period()
        time_period_dir = os.path.join(base_audio_dir, time_period)

        logger.info(f"当前时段: {time_period}，使用音频目录: {time_period_dir}")

        # 使用大小写不敏感的搜索方法
        audio_files = _find_audio_files_case_insensitive(time_period_dir)

        # 如果指定时段目录中没有音频文件，则使用基础目录
        if not audio_files:
            logger.warning(f"在时段目录 {time_period_dir} 中未找到音频文件，尝试使用基础目录")
            audio_files = _find_audio_files_case_insensitive(base_audio_dir)
    else:
        # 如果未启用基于时间的播放，直接使用基础目录
        logger.info(f"未启用基于时间的播放，使用基础音频目录: {base_audio_dir}")
        audio_files = _find_audio_files_case_insensitive(base_audio_dir)

    return audio_files

if __name__ == "__main__":
    # 测试闲置音频管理器
    initialize()
    
    print("闲置音频管理器已启动，按Ctrl+C退出...")
    try:
        # 保持主线程运行，等待定时器触发
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("正在停止闲置音频管理器...")
        stop()
        print("已退出") 