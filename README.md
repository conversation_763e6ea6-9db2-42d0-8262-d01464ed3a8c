# 恐龙语音交互系统

这是一个基于豆包API和语音合成的交互系统，支持语音唤醒、对话和播放等功能。

## 项目结构

该项目采用模块化设计，将不同功能分离为独立模块：

```
/konglong3
├── config/                  # 配置文件目录
│   └── config.yaml          # 主配置文件
├── src/                     # 源代码目录
│   ├── api/                 # API相关模块
│   │   ├── doubao_api.py    # 豆包API封装
│   │   └── tts_api.py       # TTS API封装
│   ├── audio/               # 音频处理模块
│   │   ├── player.py        # 音频播放器
│   │   └── processor.py     # 音频处理工具
│   ├── services/            # 服务模块
│   │   ├── wake_word.py     # 唤醒词检测服务
│   │   ├── record.py        # 录音服务
│   │   ├── conversation.py  # 对话管理服务
│   │   └── idle_audio.py    # 闲置音频服务
│   ├── utils/               # 工具函数
│   │   ├── config_loader.py # 配置加载工具
│   │   ├── logger.py        # 日志配置
│   │   └── text_processor.py# 文本处理工具
│   ├── state/               # 状态管理
│   │   └── state_manager.py # 状态管理器
│   └── main.py              # 主程序入口
├── run.py                   # 便捷启动脚本
├── resources/               # 资源文件目录
│   └── audio/               # 音频资源
│       ├── notification/    # 提示音
│       └── idle/            # 闲置音频
├── logs/                    # 日志文件目录
└── requirements.txt         # 依赖包列表
```

## 安装

1. 克隆项目：

```bash
git clone https://github.com/your-repo/konglong3.git
cd konglong3
```

2. 安装依赖：

```bash
pip install -r requirements.txt
```

3. 配置文件：

编辑 `config/config.yaml` 文件，设置API密钥和相关参数。

## 使用方法

### 运行方式（以下三种方式选择一种即可）

1. 使用便捷启动脚本（推荐）：

```bash
python run.py
```

2. 使用Python模块方式运行：

```bash
python -m src.main
```

3. 直接运行main.py（已添加路径修复）：

```bash
python src/main.py
```

### 测试模式

```bash
python run.py --test --query "你好，请问你是谁？"
```

### 创建测试提示音

```bash
python run.py --create-audio
```

### 指定配置文件

```bash
python run.py --config /path/to/config.yaml
```

## 路径问题说明

本项目使用了绝对导入（例如 `from src.utils.logger import logger`），因此需要确保Python能够正确找到`src`包。我们提供了三种解决方案：

1. 使用`python -m src.main`以模块方式运行
2. 使用`run.py`启动脚本（已处理路径问题）
3. 直接运行`src/main.py`（已在文件开头添加路径修复代码）

## 服务依赖

本系统依赖以下外部服务：

1. 唤醒词服务 - 默认地址 `http://127.0.0.1:8200`
2. 录音转写服务 - 默认地址 `http://127.0.0.1:8400`
3. 语音合成服务(TTS) - 可在配置中设置

## 音频空数据处理优化

为了增强系统对空音频数据的处理能力，我们对以下部分进行了优化：

1. **响应内容检查**：在获取语音合成响应后立即检查响应内容长度，确保内容足够长以构成有效音频
2. **音频数据验证**：检查音频数据是否为空，避免后续处理环节出错
3. **文件有效性验证**：多层次检查音频文件的有效性（存在性、大小、WAV格式有效性、帧数）
4. **播放前验证**：播放前确保音频文件具有有效内容
5. **全局错误处理**：增强了整体错误处理机制，确保即使出现空音频也不会导致程序崩溃
6. **空音频反馈**：当音频为空时提供清晰的错误日志，便于排查问题

这些优化确保了系统在面对各种边缘情况时能够稳定运行，不会因为空音频导致程序中断。

## 开发指南

### 添加新功能

1. 在相应目录下创建新模块
2. 在 `main.py` 中集成新功能
3. 如需添加配置，请在 `config.yaml` 中添加

### 调试日志

日志文件存储在 `logs/` 目录中，格式为 `app_YYYYMMDD_HHMMSS.log`

## 许可证

MIT 