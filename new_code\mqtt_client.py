#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步MQTT客户端模块
用于订阅MQTT消息并处理接收到的数据
"""

import json
import time
import asyncio
# from loguru import logger
from paho.mqtt import client as mqtt
from logger_config import setup_logger

# 设置日志
logger = setup_logger()

class AsyncMQTTClient:
    def __init__(self, broker_host="localhost", broker_port=1883, username=None, password=None, device_id="0007"):
        """
        初始化异步MQTT客户端
        
        Args:
            broker_host (str): MQTT服务器地址
            broker_port (int): MQTT服务器端口
            username (str): 用户名
            password (str): 密码
            device_id (str): 设备ID
        """
        self.broker_host = broker_host
        self.broker_port = broker_port
        self.username = username
        self.password = password
        self.device_id = device_id
        
        # 创建MQTT客户端
        self.client = mqtt.Client(client_id=f"subscriber_{int(time.time())}")
        
        # 设置回调函数
        self.client.on_connect = self._on_connect
        self.client.on_disconnect = self._on_disconnect
        self.client.on_message = self._on_message
        self.client.on_subscribe = self._on_subscribe
        
        # 如果有用户名和密码，设置认证
        if self.username and self.password:
            self.client.username_pw_set(self.username, self.password)
        
        # 消息处理回调
        self.message_callback = None
        
        # 连接状态
        self.is_connected = False
        
        # 事件循环
        self.loop = None
        
    def _on_connect(self, client, userdata, flags, rc):
        """连接回调函数"""
        if rc == 0:
            logger.info(f"成功连接到MQTT服务器 {self.broker_host}:{self.broker_port}")
            self.is_connected = True
        else:
            logger.error(f"连接失败，返回码: {rc}")
            self.is_connected = False
            
    def _on_disconnect(self, client, userdata, rc):
        """断开连接回调函数"""
        self.is_connected = False
        if rc != 0:
            logger.warning("意外断开连接")
        else:
            logger.info("正常断开连接")
            
    def _on_message(self, client, userdata, msg):
        """消息接收回调函数"""
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            
            logger.debug(f"收到MQTT消息 - 主题: {topic}")
            logger.debug(f"消息内容: {payload}")
            
            # 尝试解析JSON格式的消息
            try:
                data = json.loads(payload)
                # 根据主题处理消息
                if topic == "KLWOWN":
                    self._handle_klwown_message(data)
                else:
                    # 其他主题的消息直接传递给回调函数
                    if self.message_callback and self.loop:
                        asyncio.run_coroutine_threadsafe(
                            self.message_callback(topic, data),
                            self.loop
                        )
            except json.JSONDecodeError:
                logger.warning(f"消息不是JSON格式: {payload}")
                data = {"text": payload}  # 如果不是JSON，将原始文本作为text字段
                if self.message_callback and self.loop:
                    asyncio.run_coroutine_threadsafe(
                        self.message_callback(topic, data),
                        self.loop
                    )
                
        except Exception as e:
            logger.error(f"处理MQTT消息时出错: {e}")
            
    def _handle_klwown_message(self, data):
        """处理KLWOWN主题的消息"""
        try:
            logger.info("处理KLWOWN消息:")
            logger.info(f"  ID: {data.get('id', '')}")
            logger.info(f"  时间: {data.get('time', '')}")
            
            # 提取要合成语音的文本
            text = None
            if 'data' in data:
                logger.info(f"  类型: {data['data'].get('type', '')}")
                text = data['data'].get('content', '')
                logger.info(f"  内容: {text}")
            
            # 如果有文本内容且有回调函数，则调用回调
            if text and self.message_callback and self.loop:
                processed_data = {
                    "id": data.get('id', ''),
                    "time": data.get('time', ''),
                    "text": text,
                    "type": data.get('data', {}).get('type', '')
                }
                asyncio.run_coroutine_threadsafe(
                    self.message_callback("KLWOWN", processed_data),
                    self.loop
                )
                
        except Exception as e:
            logger.error(f"处理KLWOWN消息出错: {e}")
            
    def _on_subscribe(self, client, userdata, mid, granted_qos):
        """订阅成功回调函数"""
        logger.info(f"MQTT主题订阅成功，消息ID: {mid}, QoS: {granted_qos}")
        
    async def connect(self):
        """连接到MQTT服务器"""
        try:
            logger.info(f"正在连接到MQTT服务器 {self.broker_host}:{self.broker_port}...")
            self.client.connect(self.broker_host, self.broker_port, keepalive=60)
            self.client.loop_start()  # 启动后台线程处理网络事件
            
            # 保存事件循环引用
            self.loop = asyncio.get_running_loop()
            
            # 等待连接成功
            for _ in range(10):  # 最多等待10秒
                if self.is_connected:
                    return True
                await asyncio.sleep(1)
            
            return self.is_connected
            
        except Exception as e:
            logger.error(f"MQTT连接失败: {e}")
            return False
            
    def subscribe(self, topic, qos=0):
        """订阅主题"""
        try:
            logger.info(f"订阅MQTT主题: {topic}, QoS: {qos}")
            result = self.client.subscribe(topic, qos)
            if result[0] == mqtt.MQTT_ERR_SUCCESS:
                logger.info(f"MQTT主题订阅请求发送成功")
                return True
            else:
                logger.error(f"MQTT主题订阅失败，错误码: {result[0]}")
                return False
        except Exception as e:
            logger.error(f"MQTT主题订阅时出错: {e}")
            return False
            
    def disconnect(self):
        """断开连接"""
        logger.info("断开MQTT连接...")
        self.client.loop_stop()
        self.client.disconnect()
        self.loop = None
        
    def set_message_callback(self, callback):
        """设置消息处理回调函数"""
        self.message_callback = callback 