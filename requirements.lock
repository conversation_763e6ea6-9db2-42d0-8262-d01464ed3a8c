# This file was autogenerated by uv via the following command:
#    uv pip compile requirements.txt -o requirements.lock
aiohappyeyeballs==2.6.1
    # via -r requirements.txt
aiohttp==3.8.5
    # via -r requirements.txt
aiosignal==1.3.1
    # via
    #   -r requirements.txt
    #   aiohttp
annotated-types==0.7.0
    # via
    #   -r requirements.txt
    #   pydantic
anyio==4.9.0
    # via
    #   -r requirements.txt
    #   starlette
async-timeout==4.0.3
    # via aiohttp
attrs==25.3.0
    # via
    #   -r requirements.txt
    #   aiohttp
certifi==2025.1.31
    # via
    #   -r requirements.txt
    #   requests
charset-normalizer==3.4.1
    # via
    #   -r requirements.txt
    #   aiohttp
    #   requests
click==8.1.8
    # via
    #   -r requirements.txt
    #   uvicorn
colorama==0.4.6
    # via
    #   click
    #   loguru
exceptiongroup==1.3.0
    # via anyio
fastapi==0.115.12
    # via -r requirements.txt
frozenlist==1.5.0
    # via
    #   -r requirements.txt
    #   aiohttp
    #   aiosignal
h11==0.14.0
    # via
    #   -r requirements.txt
    #   uvicorn
idna==3.10
    # via
    #   -r requirements.txt
    #   anyio
    #   requests
    #   yarl
loguru==0.7.0
    # via -r requirements.txt
multidict==6.4.3
    # via
    #   -r requirements.txt
    #   aiohttp
    #   yarl
numpy==1.24.3
    # via
    #   -r requirements.txt
    #   scipy
propcache==0.3.1
    # via
    #   -r requirements.txt
    #   yarl
pyaudio==0.2.14
    # via -r requirements.txt
pydantic==2.11.3
    # via
    #   -r requirements.txt
    #   fastapi
pydantic-core==2.33.1
    # via
    #   -r requirements.txt
    #   pydantic
pydub==0.25.1
    # via -r requirements.txt
pygame==2.6.1
    # via -r requirements.txt
pymodbus==3.9.2
    # via -r requirements.txt
python-dateutil==2.9.0.post0
    # via -r requirements.txt
pyyaml==6.0.1
    # via -r requirements.txt
requests==2.31.0
    # via -r requirements.txt
scipy==1.11.2
    # via -r requirements.txt
six==1.17.0
    # via
    #   -r requirements.txt
    #   python-dateutil
sniffio==1.3.1
    # via
    #   -r requirements.txt
    #   anyio
starlette==0.46.2
    # via
    #   -r requirements.txt
    #   fastapi
typing-extensions==4.13.2
    # via
    #   -r requirements.txt
    #   anyio
    #   exceptiongroup
    #   fastapi
    #   multidict
    #   pydantic
    #   pydantic-core
    #   typing-inspection
    #   uvicorn
typing-inspection==0.4.0
    # via
    #   -r requirements.txt
    #   pydantic
urllib3==2.4.0
    # via
    #   -r requirements.txt
    #   requests
uvicorn==0.34.1
    # via -r requirements.txt
websockets==10.4
    # via -r requirements.txt
win32-setctime==1.2.0
    # via loguru
yarl==1.19.0
    # via
    #   -r requirements.txt
    #   aiohttp
