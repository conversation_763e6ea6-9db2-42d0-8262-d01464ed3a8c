# -*- coding: utf-8 -*-
"""
批量文字转语音工具
支持豆包TTS API，可以将文字列表批量转换为mp3和wav格式的音频文件
包含所有功能的完整实现
"""
import os
import sys
import time
import json
import uuid
import base64
import requests
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import subprocess
from logger_config import setup_logger

# 设置日志
logger = setup_logger(module_name="batch_tts_converter")

# 导入配置
try:
    from new_code.config_loader import get_config
except ImportError:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from new_code.config_loader import get_config

@dataclass
class TTSTask:
    """TTS任务数据类"""
    text: str
    output_name: str  # 输出文件名（不含扩展名）
    mp3_path: str = ""
    wav_path: str = ""
    success: bool = False
    error_msg: str = ""

class BatchTTSConverter:
    """批量TTS转换器 - 完整功能版本"""

    def __init__(self, output_dir: str = "output/batch_tts"):
        """
        初始化批量TTS转换器

        Args:
            output_dir: 输出目录路径
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 初始化配置
        self._load_config()

        logger.info(f"批量TTS转换器初始化完成，输出目录: {self.output_dir}")

    def _load_config(self):
        """加载配置"""
        try:
            config = get_config()
            doubao_config = config.get("api", {}).get("doubao_tts", {})

            # 豆包API参数
            self.appid = doubao_config.get("appid", "7476941471")
            self.access_token = doubao_config.get("access_token", "tJnsdwHsk8GpCClfkPuLmQsbhUosnqUT")
            self.cluster = doubao_config.get("cluster", "volcano_icl")
            self.voice_type = doubao_config.get("voice_type", "S_DTlzHdDn1")

            # API地址
            host = doubao_config.get("host", "openspeech.bytedance.com")
            self.tts_url = doubao_config.get("url", f"https://{host}/api/v1/tts")

            # 音频参数
            self.speed_ratio = doubao_config.get("speed_ratio", 1.0)
            self.volume_ratio = doubao_config.get("volume_ratio", 1.0)
            self.pitch_ratio = doubao_config.get("pitch_ratio", 1.0)

            logger.info("豆包TTS配置加载完成")

        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            raise
    
    def synthesize_single_text(self, text: str, output_name: str) -> TTSTask:
        """
        合成单个文本为语音
        
        Args:
            text: 要合成的文本
            output_name: 输出文件名（不含扩展名）
            
        Returns:
            TTSTask: 任务结果
        """
        task = TTSTask(text=text, output_name=output_name)
        
        try:
            # 清理文本
            import re
            clean_text = re.sub(r'\[.*?\]', '', text).strip()
            if not clean_text:
                task.error_msg = "文本为空"
                return task
            
            logger.info(f"开始合成语音: {clean_text[:50]}...")
            
            # 准备API请求
            request_json = {
                "app": {
                    "appid": self.appid,
                    "token": self.access_token,
                    "cluster": self.cluster
                },
                "user": {
                    "uid": str(uuid.uuid4())
                },
                "audio": {
                    "voice_type": self.voice_type,
                    "encoding": "mp3",
                    "speed_ratio": self.speed_ratio,
                    "volume_ratio": self.volume_ratio,
                    "pitch_ratio": self.pitch_ratio,
                },
                "request": {
                    "reqid": str(uuid.uuid4()),
                    "text": clean_text,
                    "text_type": "plain",
                    "operation": "query",
                    "with_frontend": 1,
                    "frontend_type": "unitTson"
                }
            }
            
            headers = {"Authorization": f"Bearer;{self.access_token}"}
            
            # 调用API
            response = requests.post(self.tts_url, json.dumps(request_json), headers=headers, timeout=60)
            
            if response.status_code == 200:
                resp_json = response.json()
                
                if "data" in resp_json:
                    # 获取Base64编码的音频数据
                    audio_data_base64 = resp_json["data"]
                    audio_data = base64.b64decode(audio_data_base64)
                    
                    # 保存MP3文件
                    mp3_path = self.output_dir / f"{output_name}.mp3"
                    with open(mp3_path, 'wb') as f:
                        f.write(audio_data)
                    task.mp3_path = str(mp3_path)
                    
                    # 转换为WAV格式
                    wav_path = self.output_dir / f"{output_name}.wav"
                    if self._convert_mp3_to_wav(str(mp3_path), str(wav_path)):
                        task.wav_path = str(wav_path)
                    
                    task.success = True
                    logger.info(f"语音合成成功: {output_name}")
                    
                else:
                    task.error_msg = f"API响应中没有音频数据: {resp_json}"
                    logger.error(task.error_msg)
            else:
                task.error_msg = f"API请求失败: {response.status_code} - {response.text}"
                logger.error(task.error_msg)
                
        except Exception as e:
            task.error_msg = f"合成失败: {str(e)}"
            logger.error(task.error_msg)
        
        return task
    
    def _convert_mp3_to_wav(self, mp3_path: str, wav_path: str) -> bool:
        """
        将MP3文件转换为WAV格式
        
        Args:
            mp3_path: MP3文件路径
            wav_path: WAV文件路径
            
        Returns:
            bool: 转换是否成功
        """
        try:
            # 尝试使用ffmpeg转换
            cmd = [
                'ffmpeg', '-i', mp3_path, '-acodec', 'pcm_s16le', 
                '-ar', '16000', '-ac', '1', wav_path, '-y'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"成功转换为WAV: {wav_path}")
                return True
            else:
                logger.warning(f"ffmpeg转换失败: {result.stderr}")
                
                # 尝试使用pydub作为备选方案
                return self._convert_with_pydub(mp3_path, wav_path)
                
        except FileNotFoundError:
            logger.warning("ffmpeg未找到，尝试使用pydub转换")
            return self._convert_with_pydub(mp3_path, wav_path)
        except Exception as e:
            logger.error(f"转换MP3到WAV失败: {e}")
            return False
    
    def _convert_with_pydub(self, mp3_path: str, wav_path: str) -> bool:
        """
        使用pydub转换音频格式
        
        Args:
            mp3_path: MP3文件路径
            wav_path: WAV文件路径
            
        Returns:
            bool: 转换是否成功
        """
        try:
            from pydub import AudioSegment
            
            # 加载MP3文件
            audio = AudioSegment.from_mp3(mp3_path)
            
            # 转换为16kHz单声道WAV
            audio = audio.set_frame_rate(16000).set_channels(1)
            
            # 导出为WAV
            audio.export(wav_path, format="wav")
            
            logger.info(f"使用pydub成功转换为WAV: {wav_path}")
            return True
            
        except ImportError:
            logger.error("pydub未安装，无法转换音频格式")
            return False
        except Exception as e:
            logger.error(f"pydub转换失败: {e}")
            return False
    
    def _generate_filename_from_text(self, text: str, max_length: int = 20) -> str:
        """
        根据文本内容生成文件名

        Args:
            text: 文本内容
            max_length: 文件名最大长度

        Returns:
            str: 生成的文件名
        """
        import re

        # 清理文本，移除动作标记和特殊字符
        clean_text = re.sub(r'\[.*?\]', '', text)
        clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', clean_text)

        # 截取前面部分作为文件名
        if len(clean_text) > max_length:
            filename = clean_text[:max_length]
        else:
            filename = clean_text

        # 如果文件名为空，使用默认名称
        if not filename:
            filename = "audio"

        return filename

    def batch_synthesize(self, texts, name_prefix: str = None, use_text_as_name: bool = True) -> List[TTSTask]:
        """
        批量合成语音 - 支持文本列表或单个文本

        Args:
            texts: 要合成的文本（字符串或字符串列表）
            name_prefix: 输出文件名前缀（当use_text_as_name=False时使用）
            use_text_as_name: 是否使用文本内容作为文件名

        Returns:
            List[TTSTask]: 任务结果列表
        """
        # 统一处理输入格式
        if isinstance(texts, str):
            text_list = [texts]
        else:
            text_list = texts

        logger.info(f"开始批量合成 {len(text_list)} 个文本")

        results = []

        for i, text in enumerate(text_list):
            if use_text_as_name:
                # 使用文本内容生成文件名
                output_name = self._generate_filename_from_text(text)
                # 如果有重复，添加序号
                if any(task.output_name == output_name for task in results):
                    output_name = f"{output_name}_{i+1}"
            else:
                # 使用前缀和序号
                if name_prefix is None:
                    name_prefix = "audio"
                if len(text_list) == 1:
                    output_name = name_prefix
                else:
                    output_name = f"{name_prefix}_{i+1:03d}"

            logger.info(f"处理第 {i+1}/{len(text_list)} 个文本: {output_name}")

            task = self.synthesize_single_text(text, output_name)
            results.append(task)

            # 添加延迟避免API限制
            if i < len(text_list) - 1:
                time.sleep(0.5)

        # 统计结果
        success_count = sum(1 for task in results if task.success)
        logger.info(f"批量合成完成: 成功 {success_count}/{len(text_list)}")

        return results
    
    def batch_synthesize_with_names(self, text_dict: Dict[str, str]) -> List[TTSTask]:
        """
        批量合成语音（指定文件名）
        
        Args:
            text_dict: 文本字典，键为文件名，值为文本内容
            
        Returns:
            List[TTSTask]: 任务结果列表
        """
        logger.info(f"开始批量合成 {len(text_dict)} 个文本（指定文件名）")
        
        results = []
        
        for i, (name, text) in enumerate(text_dict.items()):
            logger.info(f"处理第 {i+1}/{len(text_dict)} 个文本: {name}")
            
            task = self.synthesize_single_text(text, name)
            results.append(task)
            
            # 添加延迟避免API限制
            if i < len(text_dict) - 1:
                time.sleep(0.5)
        
        # 统计结果
        success_count = sum(1 for task in results if task.success)
        logger.info(f"批量合成完成: 成功 {success_count}/{len(text_dict)}")
        
        return results
    
    def print_results(self, results: List[TTSTask]):
        """
        打印合成结果
        
        Args:
            results: 任务结果列表
        """
        print("\n=== 批量TTS合成结果 ===")
        
        for i, task in enumerate(results, 1):
            print(f"\n{i}. {task.output_name}")
            print(f"   文本: {task.text[:50]}{'...' if len(task.text) > 50 else ''}")
            
            if task.success:
                print(f"   ✓ 成功")
                if task.mp3_path:
                    print(f"   MP3: {task.mp3_path}")
                if task.wav_path:
                    print(f"   WAV: {task.wav_path}")
            else:
                print(f"   ✗ 失败: {task.error_msg}")
        
        success_count = sum(1 for task in results if task.success)
        print(f"\n总计: 成功 {success_count}/{len(results)}")


if __name__ == "__main__":
    # 使用示例
    converter = BatchTTSConverter("output/batch_tts")

    # 使用文本内容作为文件名（默认）
    texts = ["在呢", "什么事"]
    results = converter.batch_synthesize(texts)

    # 使用前缀命名
    results2 = converter.batch_synthesize(texts, "demo", use_text_as_name=False)

    # 打印结果
    converter.print_results(results + results2)
